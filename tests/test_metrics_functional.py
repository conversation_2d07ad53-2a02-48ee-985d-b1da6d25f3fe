import os
import time
import unittest
from datetime import datetime, timedelta

import requests
from fastapi import status

try:
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    # dotenv is optional
    pass
from requests.adapters import HTTPAdapter  # type: ignore
from requests.packages.urllib3.util.retry import Retry  # type: ignore

retry_strategy = Retry(
    total=3,  # Total number of retries (including the initial request)
    status_forcelist=[500, 423],  # HTTP status codes to retry
    backoff_factor=0.5,  # Factor by which the delay increases after each retry
    method_whitelist=["GET", "POST"],  # HTTP methods to retry
)

adapter = HTTPAdapter(max_retries=retry_strategy)
client: requests.sessions.Session = requests.Session()
client.mount("http://", adapter)
client.mount("https://", adapter)


# No authentication required for metrics endpoints


class TestMetrics(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.url = "http://localhost:8071"
        cls.TEST_ACCOUNT_ID = os.environ.get("TEST_ACCOUNT_ID", None)
        assert cls.TEST_ACCOUNT_ID is not None, "TEST_ACCOUNT_ID not set"
        cls.user = os.environ.get("TEST_USER", None)
        assert cls.user is not None, "TEST_USER not set"
        cls.bucket = os.environ.get("TEST_BUCKET", None)
        assert cls.bucket is not None, "TEST_BUCKET not set"
        cls.region = os.environ.get("TEST_REGION", None)
        assert cls.region is not None, "TEST_REGION not set"

    def _make_request(self, endpoint: str, params: dict):
        """Helper method to make requests (no authentication required)"""
        try:
            r = client.get(endpoint, params=params)
            if r.status_code == status.HTTP_501_NOT_IMPLEMENTED:
                print("🚧 Not implemented")
                return r
            r.raise_for_status()
            return r
        except requests.exceptions.HTTPError as e:
            if e.response.status_code in [500, 423]:
                # Retry once for server errors
                time.sleep(1)
                r = client.get(endpoint, params=params)
                r.raise_for_status()
                return r
            else:
                raise e

    def _validate_metrics_response(self, response_data: dict, expected_type: str):
        """Helper method to validate metrics response structure"""
        # Check top-level structure
        self.assertIn("metrics", response_data)
        self.assertIsInstance(response_data["metrics"], list)

        # Check type-specific fields
        if expected_type == "account":
            self.assertIn("accountId", response_data)
            self.assertEqual(response_data["accountId"], self.TEST_ACCOUNT_ID)
        elif expected_type == "user":
            self.assertIn("userId", response_data)
            self.assertEqual(response_data["userId"], self.user)
        elif expected_type == "bucket":
            self.assertIn("bucketName", response_data)
            self.assertEqual(response_data["bucketName"], self.bucket)

        # Validate metrics structure if present
        if response_data["metrics"]:
            for metric in response_data["metrics"]:
                self.assertIn("interval", metric)
                self.assertIn("startDate", metric)
                self.assertIn("endDate", metric)
                # Check for either storageUtilized or storageUtilized(delta)
                self.assertTrue(
                    "storageUtilized" in metric or "storageUtilized(delta)" in metric,
                    "Expected 'storageUtilized' or 'storageUtilized(delta)' in metric",
                )
                self.assertTrue(
                    "numberOfObjects" in metric or "numberOfObjects(delta)" in metric,
                    "Expected 'numberOfObjects' or 'numberOfObjects(delta)' in metric",
                )
                self.assertIn("numberOfOperations", metric)
                self.assertIsInstance(metric["numberOfOperations"], dict)

    # Account Metrics Tests
    def test_account_metrics_current(self):
        """Test account metrics with current timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"
        params = {"region": self.region, "current": True}

        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_week(self):
        """Test account metrics with week timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"
        params = {"region": self.region, "week": True, "current": False}

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_month(self):
        """Test account metrics with month timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"
        params = {"region": self.region, "month": True, "current": False}

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_year(self):
        """Test account metrics with year timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"
        params = {"region": self.region, "year": True, "current": False}

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_custom(self):
        """Test account metrics with custom timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"

        # Custom timeframe: last 24 hours
        end_time = int(datetime.now().timestamp())
        start_time = int((datetime.now() - timedelta(days=1)).timestamp())

        params = {
            "region": self.region,
            "custom": True,
            "current": False,
            "start_time": start_time,
            "end_time": end_time,
        }

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_human_readable(self):
        """Test account metrics with human readable format"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"
        params = {"region": self.region, "current": True, "human": True}

        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_multiple_timeframes(self):
        """Test account metrics with multiple timeframes"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"
        params = {"region": self.region, "current": True, "week": True, "month": True}

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            response_data = r.json()
            self._validate_metrics_response(response_data, "account")

            # Should have metrics for multiple intervals
            intervals = [metric["interval"] for metric in response_data["metrics"]]
            self.assertIn("current", intervals)
            self.assertIn("week", intervals)
            self.assertIn("month", intervals)

    def test_account_metrics_no_timeframe_error(self):
        """Test account metrics with no timeframe selected returns error"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"
        params = {
            "region": self.region,
            "current": False,
            "week": False,
            "month": False,
            "year": False,
            "custom": False,
        }

        r = client.get(endpoint, params=params)
        self.assertEqual(r.status_code, 400)

    # User Metrics Tests
    def test_user_metrics_current(self):
        """Test user metrics with current timeframe"""
        endpoint = f"{self.url}/metrics/user/{self.user}"
        params = {"region": self.region, "current": True}

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            self._validate_metrics_response(r.json(), "user")

    def test_user_metrics_custom(self):
        """Test user metrics with custom timeframe"""
        endpoint = f"{self.url}/metrics/user/{self.user}"

        # Custom timeframe: last 24 hours
        end_time = int(datetime.now().timestamp())
        start_time = int((datetime.now() - timedelta(days=1)).timestamp())

        params = {
            "region": self.region,
            "custom": True,
            "current": False,
            "start_time": start_time,
            "end_time": end_time,
        }

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            self._validate_metrics_response(r.json(), "user")

    # Bucket Metrics Tests
    def test_bucket_metrics_current(self):
        """Test bucket metrics with current timeframe"""
        endpoint = f"{self.url}/metrics/bucket/{self.bucket}"
        params = {"region": self.region, "current": True}

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            self._validate_metrics_response(r.json(), "bucket")

    def test_bucket_metrics_week(self):
        """Test bucket metrics with week timeframe"""
        endpoint = f"{self.url}/metrics/bucket/{self.bucket}"
        params = {"region": self.region, "week": True, "current": False}

        r = self._make_request(endpoint, params)
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])
        if r.status_code == 200:
            self._validate_metrics_response(r.json(), "bucket")

    # Error Handling Tests
    def test_account_metrics_invalid_account(self):
        """Test account metrics with invalid account ID"""
        endpoint = f"{self.url}/metrics/account/invalid-account-id"
        params = {"region": self.region, "current": True}

        r = client.get(endpoint, params=params)
        self.assertEqual(r.status_code, 404)

    def test_user_metrics_invalid_user(self):
        """Test user metrics with invalid user"""
        endpoint = f"{self.url}/metrics/user/invalid-user"
        params = {"region": self.region, "current": True}

        r = client.get(endpoint, params=params)
        # return 200 with all metrics at zero
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])

    def test_bucket_metrics_invalid_bucket(self):
        """Test bucket metrics with invalid bucket"""
        endpoint = f"{self.url}/metrics/bucket/invalid-bucket"
        params = {"region": self.region, "current": True}

        r = client.get(endpoint, params=params)
        # return 200 with all metrics at zero
        self.assertIn(r.status_code, [200, status.HTTP_501_NOT_IMPLEMENTED])

    # Performance Tests
    def test_metrics_response_time(self):
        """Test that metrics endpoints respond within reasonable time"""
        endpoint = f"{self.url}/metrics/account/{self.TEST_ACCOUNT_ID}"
        params = {"region": self.region, "current": True}

        start_time = time.time()
        r = self._make_request(endpoint, params)
        response_time = time.time() - start_time

        self.assertEqual(r.status_code, 200)
        self.assertLess(response_time, 10.0, "Metrics response took too long")


if __name__ == "__main__":
    unittest.main()
