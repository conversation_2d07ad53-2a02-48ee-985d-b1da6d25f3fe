"""
Unit tests for router integration with provider abstraction layer.

This test suite validates that the routers correctly use the provider factory
and that the dependency injection works as expected.
"""

import os
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

# Mock environment variables before importing anything that uses ApplicationSettings
os.environ.setdefault("IDP_ROOT_URI", "http://localhost:8080")
os.environ.setdefault("GATEWAY_API_URL", "http://localhost:8000")
os.environ.setdefault("IDENTITY_PROVIDER_URL", "http://localhost:8080")
os.environ.setdefault("IDENTITY_PROVIDER_REALM", "test")
os.environ.setdefault("CLIENT_ID", "test-client")
os.environ.setdefault("CLIENT_SECRET", "test-secret")

# Mock authentication before importing routers
with patch("fp_aaa_kit.validate_user") as mock_validate_user:
    mock_validate_user.return_value = lambda: Mock(sub="test-user")

    from api.config import Regions
    from api.routers.console import router as console_router
    from api.routers.metrics import router as metrics_router
    from api.routers.vault import router as vault_router


class TestVaultRouterProviderIntegration:
    """Test vault router integration with providers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.app = FastAPI()
        self.app.include_router(vault_router)
        self.client = TestClient(self.app)

    @patch("api.routers.vault.ProviderFactory.get_account_provider")
    @patch("api.routers.vault.Cache")
    def test_get_account_uses_provider_factory(
        self, mock_cache, mock_get_account_provider
    ):
        """Test that GetAccount endpoint uses ProviderFactory."""
        # Create a new app for this test with dependency override
        test_app = FastAPI()

        # Setup authentication mock
        def mock_auth_dependency():
            mock_user = Mock()
            mock_user.sub = "test-user"
            return mock_user

        # Override the authentication dependency before including router
        from fp_aaa_kit import AnyRoleIn, validate_user

        test_app.dependency_overrides[
            validate_user(AnyRoleIn("jso_admin_api_read"))
        ] = mock_auth_dependency

        # Include the router after setting up overrides
        test_app.include_router(vault_router)
        test_client = TestClient(test_app)

        # Setup cache mock to avoid cache hits
        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance
        mock_cache_instance.hit.return_value = False

        # Setup provider mock
        mock_provider = Mock()
        mock_get_account_provider.return_value = mock_provider
        mock_provider.get_account = AsyncMock(
            return_value=[{"name": "test-account", "id": "123"}]
        )

        # Make request
        response = test_client.get(
            "/vault/account/test-account",
            params={"region": "fr-par"},
            headers={"Authorization": "Bearer fake-token"},
        )

        # Debug: print response details
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.content}")

        # Verify provider factory was used
        mock_get_account_provider.assert_called_once_with(Regions.fr_par)
        mock_provider.get_account.assert_called_once_with(
            "test-account", starts_with=False, ends_with=False
        )

    @patch("api.routers.vault.ProviderFactory")
    @patch("fp_aaa_kit.validate_user")
    def test_get_account_buckets_uses_provider_factory(
        self, mock_auth, mock_provider_factory
    ):
        """Test that GetAccountBuckets endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}

        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory

        mock_provider = Mock()
        mock_factory.get_account_provider.return_value = mock_provider
        mock_provider.get_account_buckets = AsyncMock(
            return_value=["bucket1", "bucket2"]
        )

        # Make request
        self.client.get(
            "/vault/account/test-account/buckets",
            params={"region": "fr-par"},
            headers={"Authorization": "Bearer fake-token"},
        )

        # Verify provider factory was used
        mock_factory.get_account_provider.assert_called_once_with(Regions.fr_par)
        mock_provider.get_account_buckets.assert_called_once_with("test-account")


class TestMetricsRouterProviderIntegration:
    """Test metrics router integration with providers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.app = FastAPI()
        self.app.include_router(metrics_router)
        self.client = TestClient(self.app)

    @patch("api.routers.metrics.ProviderFactory")
    @patch("fp_aaa_kit.validate_user")
    def test_get_metrics_uses_provider_factory(self, mock_auth, mock_provider_factory):
        """Test that GetMetrics endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}

        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory

        mock_provider = Mock()
        mock_factory.get_metrics_provider.return_value = mock_provider
        mock_provider.get_metrics = AsyncMock(
            return_value={"objects": 100, "bytes": 1024}
        )

        # Make request
        self.client.get(
            "/metrics/test-account/bucket",
            params={"region": "fr-par"},
            headers={"Authorization": "Bearer fake-token"},
        )

        # Verify provider factory was used
        mock_factory.get_metrics_provider.assert_called_once_with(Regions.fr_par)
        mock_provider.get_metrics.assert_called_once_with("test-account", "bucket")

    @patch("api.routers.metrics.ProviderFactory")
    @patch("fp_aaa_kit.validate_user")
    def test_get_account_metrics_uses_provider_factory(
        self, mock_auth, mock_provider_factory
    ):
        """Test that GetAccountMetrics endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}

        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory

        mock_provider = Mock()
        mock_factory.get_metrics_provider.return_value = mock_provider
        mock_provider.get_account_metrics = AsyncMock(
            return_value={"total_objects": 500}
        )

        # Make request
        self.client.get(
            "/metrics/test-account",
            params={"region": "fr-par"},
            headers={"Authorization": "Bearer fake-token"},
        )

        # Verify provider factory was used
        mock_factory.get_metrics_provider.assert_called_once_with(Regions.fr_par)
        mock_provider.get_account_metrics.assert_called_once_with("test-account")


class TestConsoleRouterProviderIntegration:
    """Test console router integration with providers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.app = FastAPI()
        self.app.include_router(console_router)
        self.client = TestClient(self.app)

    @patch("api.routers.console.ProviderFactory")
    @patch("fp_aaa_kit.validate_user")
    def test_get_console_account_uses_provider_factory(
        self, mock_auth, mock_provider_factory
    ):
        """Test that GetConsoleAccount endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}

        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory

        mock_provider = Mock()
        mock_factory.get_console_provider.return_value = mock_provider
        mock_provider.get_account = AsyncMock(
            return_value={"id": "123", "name": "test-account"}
        )

        # Make request
        self.client.get(
            "/console/account/test-account",
            params={"region": "fr-par"},
            headers={"Authorization": "Bearer fake-token"},
        )

        # Verify provider factory was used
        mock_factory.get_console_provider.assert_called_once_with(Regions.fr_par)
        mock_provider.get_account.assert_called_once_with("test-account")

    @patch("api.routers.console.ProviderFactory")
    @patch("fp_aaa_kit.validate_user")
    def test_create_console_account_uses_multiple_providers(
        self, mock_auth, mock_provider_factory
    ):
        """Test that CreateConsoleAccount endpoint uses multiple providers."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}

        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory

        # Mock account provider (to check if account exists)
        mock_account_provider = Mock()
        mock_factory.get_account_provider.return_value = mock_account_provider
        mock_account_provider.get_account = AsyncMock(
            return_value=None
        )  # Account doesn't exist

        # Mock console provider (to create account)
        mock_console_provider = Mock()
        mock_factory.get_console_provider.return_value = mock_console_provider
        mock_console_provider.create_account = AsyncMock(
            return_value={"id": "456", "name": "new-account"}
        )

        # Make request
        self.client.post(
            "/console/account/new-account",
            params={"region": "fr-par"},
            headers={"Authorization": "Bearer fake-token"},
        )

        # Verify both providers were used
        mock_factory.get_account_provider.assert_called_once_with(Regions.fr_par)
        mock_factory.get_console_provider.assert_called_once_with(Regions.fr_par)
        mock_account_provider.get_account.assert_called_once_with("new-account")
        mock_console_provider.create_account.assert_called_once_with("new-account")


class TestProviderDependencyInjection:
    """Test that provider dependency injection works correctly."""

    @patch("api.providers.factory.ProviderFactory")
    def test_provider_factory_singleton_behavior(self, mock_provider_factory_class):
        """Test that ProviderFactory behaves as expected in dependency injection."""
        # Import the dependency functions
        from api.routers.console import (
            get_account_provider as console_get_account_provider,
        )
        from api.routers.console import get_console_provider
        from api.routers.vault import get_account_provider

        # Setup mock
        mock_factory_instance = Mock()
        mock_provider_factory_class.return_value = mock_factory_instance

        mock_account_provider = Mock()
        mock_metrics_provider = Mock()
        mock_console_provider = Mock()

        mock_factory_instance.get_account_provider.return_value = mock_account_provider
        mock_factory_instance.get_metrics_provider.return_value = mock_metrics_provider
        mock_factory_instance.get_console_provider.return_value = mock_console_provider

        # Test dependency functions
        account_provider = get_account_provider(Regions.fr_par)
        console_provider = get_console_provider(Regions.fr_par)
        console_account_provider = console_get_account_provider(Regions.fr_par)

        # Verify correct providers are returned
        assert account_provider == mock_account_provider
        assert console_provider == mock_console_provider
        assert console_account_provider == mock_account_provider

        # Verify factory methods were called with correct regions
        assert (
            mock_factory_instance.get_account_provider.call_count == 2
        )  # Called twice
        assert mock_factory_instance.get_metrics_provider.call_count == 1
        assert mock_factory_instance.get_console_provider.call_count == 1


if __name__ == "__main__":
    pytest.main([__file__])
