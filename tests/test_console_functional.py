import configparser
import os
import time
import unittest
import uuid

import boto3
import httpx
import hvac
import urllib3
from botocore.exceptions import Client<PERSON>rror
from fastapi import status

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

config = configparser.ConfigParser(interpolation=None)
config.read_file(open("api/.config"))


def get_token(client_id: str, client_secret: str) -> str:
    """Get OAuth2 token for API authentication"""
    endpoint = os.getenv("IDENTITY_PROVIDER_TOKEN_URL", "")
    headers = {
        "accept": "application/json",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    payload = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret,
    }

    with httpx.Client(timeout=30.0) as client:
        response = client.post(endpoint, headers=headers, data=payload)
        response.raise_for_status()
        return response.json()["access_token"]


class TestConsole(unittest.TestCase):
    """Functional test suite for console account creation and deletion endpoints"""

    @classmethod
    def setUpClass(cls):
        """Set up test environment and authentication"""
        cls.base_url = os.getenv("JSO_API_URL", "http://localhost:8071")
        cls.region = os.getenv("TEST_REGION", "")

        # Authentication setup
        cls.client_id = os.environ["CLIENT_ID_JSO"]

        cls.client_secret = os.environ["CLIENT_SECRET_JSO"]

        # Get authentication token
        cls.token = get_token(cls.client_id, cls.client_secret)
        assert cls.token is not None, "Failed to get authentication token"

        # Test account names (using UUID to avoid conflicts)
        cls.test_account_prefix = f"test-console-{uuid.uuid4().hex[:8]}"
        cls.created_accounts = []  # Track created accounts for cleanup

    @classmethod
    def tearDownClass(cls):
        """Clean up any remaining test accounts"""
        for account_name in cls.created_accounts:
            try:
                cls._delete_account_if_exists(account_name)
            except Exception as e:
                print(f"Warning: Failed to cleanup account {account_name}: {e}")

    @classmethod
    def _delete_account_if_exists(cls, account_name: str):
        """Helper method to delete account if it exists"""
        headers = {"Authorization": f"Bearer {cls.token}"}
        params = {"region": cls.region, "remove_buckets": True}

        with httpx.Client(timeout=60.0) as client:
            response = client.delete(
                f"{cls.base_url}/console/account/{account_name}",
                headers=headers,
                params=params,
            )
            # Don't raise for 404 (account doesn't exist)
            if response.status_code not in [204, 404]:
                response.raise_for_status()

    def setUp(self):
        """Set up for each test"""
        self.headers = {"Authorization": f"Bearer {self.token}"}
        self.client = httpx.Client(timeout=60.0)

    def tearDown(self):
        """Clean up after each test"""
        self.client.close()

    def test_create_console_account_success(self):
        """Test successful console account creation"""
        account_name = f"{self.test_account_prefix}-create-success"

        # Account creation payload
        payload = {
            "accountName": account_name,
            "console": True,
            "quota": **********,  # 1GB in bytes
        }

        params = {"region": self.region}

        try:
            # Create account
            response = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=params,
            )

            # Verify response
            self.assertEqual(response.status_code, 201)

            response_data = response.json()
            self.assertIn("accountId", response_data)
            self.assertIn("accountName", response_data)
            self.assertIn("emailAddress", response_data)
            self.assertIn("consolePassword", response_data)
            self.assertIn("accessKey", response_data)
            self.assertIn("secretAccessKey", response_data)
            self.assertIn("endpointUrl", response_data)
            self.assertIn("vault_url", response_data)
            self.assertIn("vault_secret_path", response_data)

            # Verify account details
            self.assertEqual(response_data["accountName"], account_name)
            self.assertEqual(
                response_data["emailAddress"], f"{account_name}@freepro.com"
            )
            self.assertEqual(response_data["accessKey"], "")
            self.assertNotEqual(response_data["vault_url"], "")
            self.assertNotEqual(response_data["vault_secret_path"], "")
            # Track for cleanup
            self.created_accounts.append(account_name)
        except Exception as e:
            self.fail(f"Account creation failed: {e}")

    def test_create_non_console_account_success(self):
        """Test successful non-console account creation"""
        account_name = f"{self.test_account_prefix}-non-console"

        # Account creation payload
        payload = {
            "accountName": account_name,
            "console": False,
            "quota": **********,  # 2GB in bytes
        }

        params = {"region": self.region}

        try:
            # Create account
            response = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=params,
            )

            # Verify response
            self.assertEqual(response.status_code, 201)

            response_data = response.json()
            self.assertEqual(response_data["accountName"], account_name)
            self.assertEqual(
                response_data["consolePassword"], ""
            )  # No console password for non-console accounts

            # Track for cleanup
            self.created_accounts.append(account_name)

        except Exception as e:
            self.fail(f"Non-console account creation failed: {e}")

    def test_create_account_duplicate_name_conflict(self):
        """Test account creation with duplicate name returns 409 conflict"""
        account_name = f"{self.test_account_prefix}-duplicate"

        payload = {"accountName": account_name, "console": True, "quota": **********}

        params = {"region": self.region}

        try:
            # Create first account
            response1 = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=params,
            )
            self.assertEqual(response1.status_code, status.HTTP_201_CREATED)
            self.created_accounts.append(account_name)

            # Try to create duplicate account
            response2 = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=params,
            )

            # Should return 409 Conflict
            self.assertEqual(response2.status_code, status.HTTP_409_CONFLICT)

        except Exception as e:
            self.fail(f"Duplicate account test failed: {e}")

    def test_create_account_invalid_quota(self):
        """Test account creation with invalid quota"""
        account_name = f"{self.test_account_prefix}-invalid-quota"

        payload = {
            "accountName": account_name,
            "console": True,
            "quota": -1,  # Invalid negative quota
        }

        params = {"region": self.region}

        response = self.client.post(
            f"{self.base_url}/console/account",
            json=payload,
            headers=self.headers,
            params=params,
        )

        # Should return 422 Unprocessable Entity for validation error
        self.assertEqual(response.status_code, 422)

    def test_delete_account_success(self):
        """Test successful account deletion"""
        account_name = f"{self.test_account_prefix}-delete-success"

        # First create an account
        payload = {"accountName": account_name, "console": True, "quota": **********}

        params = {"region": self.region}

        # Create account
        create_response = self.client.post(
            f"{self.base_url}/console/account",
            json=payload,
            headers=self.headers,
            params=params,
        )
        self.assertEqual(create_response.status_code, 201)

        # Wait a moment for account to be fully created
        time.sleep(2)

        # Delete account
        delete_params = {"region": self.region, "remove_buckets": False}
        delete_response = self.client.delete(
            f"{self.base_url}/console/account/{account_name}",
            headers=self.headers,
            params=delete_params,
        )

        # Should return 204 No Content
        self.assertEqual(delete_response.status_code, 204)

        # Account should not be in cleanup list since it's deleted
        if account_name in self.created_accounts:
            self.created_accounts.remove(account_name)

    def test_delete_nonexistent_account(self):
        """Test deletion of non-existent account returns 404"""
        nonexistent_account = f"{self.test_account_prefix}-nonexistent"

        params = {"region": self.region, "remove_buckets": False}

        response = self.client.delete(
            f"{self.base_url}/console/account/{nonexistent_account}",
            headers=self.headers,
            params=params,
        )

        # Should return 404 Not Found
        self.assertEqual(response.status_code, 404)

    def test_delete_account_with_force_bucket_removal(self):
        """Test account deletion with force bucket removal"""
        account_name = f"{self.test_account_prefix}-force-delete"

        # Create account first
        payload = {"accountName": account_name, "console": True, "quota": **********}

        params = {"region": self.region}

        create_response = self.client.post(
            f"{self.base_url}/console/account",
            json=payload,
            headers=self.headers,
            params=params,
        )
        self.assertEqual(create_response.status_code, 201)

        # Wait for account creation
        time.sleep(2)

        # Delete with force bucket removal
        delete_params = {"region": self.region, "remove_buckets": True}
        delete_response = self.client.delete(
            f"{self.base_url}/console/account/{account_name}",
            headers=self.headers,
            params=delete_params,
        )

        self.assertEqual(delete_response.status_code, 204)

    def test_unauthorized_access(self):
        """Test that endpoints require proper authentication"""
        # Test without authorization header
        payload = {
            "accountName": "test-unauthorized",
            "console": True,
            "quota": **********,
        }

        params = {"region": self.region}

        response = self.client.post(
            f"{self.base_url}/console/account", json=payload, params=params
        )

        # Should return 401 Unauthorized or 403 Forbidden
        self.assertIn(response.status_code, [401, 403])

    def test_invalid_region(self):
        """Test account creation with invalid region"""
        account_name = f"{self.test_account_prefix}-invalid-region"

        payload = {"accountName": account_name, "console": True, "quota": **********}

        params = {"region": "invalid-region"}

        response = self.client.post(
            f"{self.base_url}/console/account",
            json=payload,
            headers=self.headers,
            params=params,
        )

        # Should return 422 for invalid region enum value
        self.assertEqual(response.status_code, 422)

    def test_force_cust_parameter_true(self):
        """Test account creation with force_cust=True generates customer vault path"""
        account_name = f"{self.test_account_prefix}-force-cust-true"

        # Account creation payload
        payload = {
            "accountName": account_name,
            "console": True,
            "quota": **********,  # 1GB in bytes
        }

        # Set force_cust=True in query parameters
        params = {"region": self.region, "force_cust": True}

        try:
            # Create account with force_cust=True
            response = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=params,
            )

            # Verify response
            self.assertEqual(response.status_code, 201)

            response_data = response.json()
            vault_secret_path = response_data["vault_secret_path"]

            # Verify that the vault path contains "/cust/" when force_cust=True
            self.assertIn("/cust/", vault_secret_path)
            self.assertNotIn("/corp/", vault_secret_path)

            # Track for cleanup
            self.created_accounts.append(account_name)

        except Exception as e:
            self.fail(f"Force cust=True test failed: {e}")

    def test_force_cust_parameter_false(self):
        """Test account creation with force_cust=False generates corporate vault path"""
        account_name = f"{self.test_account_prefix}-force-cust-false"

        # Account creation payload
        payload = {
            "accountName": account_name,
            "console": True,
            "quota": **********,  # 1GB in bytes
        }

        # Set force_cust=False in query parameters (explicit false)
        params = {"region": self.region, "force_cust": False}

        try:
            # Create account with force_cust=False
            response = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=params,
            )

            # Verify response
            self.assertEqual(response.status_code, 201)

            response_data = response.json()
            vault_secret_path = response_data["vault_secret_path"]

            # Verify that the vault path contains "/corp/" when force_cust=False
            # (assuming the test user is not the si_username)
            self.assertIn("/corp/", vault_secret_path)
            self.assertNotIn("/cust/", vault_secret_path)

            # Track for cleanup
            self.created_accounts.append(account_name)

        except Exception as e:
            self.fail(f"Force cust=False test failed: {e}")

    def test_force_cust_parameter_default(self):
        """Test account creation without force_cust parameter uses default behavior"""
        account_name = f"{self.test_account_prefix}-force-cust-default"

        # Account creation payload
        payload = {
            "accountName": account_name,
            "console": True,
            "quota": **********,  # 1GB in bytes
        }

        # Don't include force_cust parameter (should default to False)
        params = {"region": self.region}

        try:
            # Create account without force_cust parameter
            response = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=params,
            )

            # Verify response
            self.assertEqual(response.status_code, 201)

            response_data = response.json()
            vault_secret_path = response_data["vault_secret_path"]

            # Verify that the vault path contains "/corp/" when force_cust is not specified
            # (assuming the test user is not the si_username)
            self.assertIn("/corp/", vault_secret_path)
            self.assertNotIn("/cust/", vault_secret_path)

            # Track for cleanup
            self.created_accounts.append(account_name)

        except Exception as e:
            self.fail(f"Force cust default test failed: {e}")

    def test_force_cust_parameter_in_delete_endpoint(self):
        """Test that force_cust parameter works correctly in delete endpoint"""
        account_name = f"{self.test_account_prefix}-force-cust-delete"

        # First create an account with force_cust=True
        payload = {
            "accountName": account_name,
            "console": True,
            "quota": **********,
        }

        create_params = {"region": self.region, "force_cust": True}

        try:
            # Create account
            create_response = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=create_params,
            )
            self.assertEqual(create_response.status_code, 201)

            # Wait for account creation
            time.sleep(2)

            # Delete account with force_cust=True (should match creation)
            delete_params = {
                "region": self.region,
                "remove_buckets": False,
                "force_cust": True,
            }
            delete_response = self.client.delete(
                f"{self.base_url}/console/account/{account_name}",
                headers=self.headers,
                params=delete_params,
            )

            # Should return 204 No Content
            self.assertEqual(delete_response.status_code, 204)

            # Account should not be in cleanup list since it's deleted
            if account_name in self.created_accounts:
                self.created_accounts.remove(account_name)

        except Exception as e:
            # Track for cleanup in case of failure
            self.created_accounts.append(account_name)
            self.fail(f"Force cust delete test failed: {e}")

    def test_account_lifecycle_with_bucket_creation(self):
        """Test complete account lifecycle: create account, create bucket, attempt delete, cleanup, delete"""
        account_name = f"{self.test_account_prefix}-lifecycle-bucket"
        bucket_name = f"test-bucket-{uuid.uuid4().hex[:8]}"

        # Step 1: Create account
        payload = {
            "accountName": account_name,
            "console": True,
            "quota": **********,  # 2GB
        }
        params = {"region": self.region}

        try:
            # Create account
            print(f"Creating account: {account_name}")
            create_response = self.client.post(
                f"{self.base_url}/console/account",
                json=payload,
                headers=self.headers,
                params=params,
            )
            self.assertEqual(create_response.status_code, 201)

            response_data = create_response.json()
            endpoint_url = response_data["endpointUrl"]

            # Get account credentials from Vault
            vault_url = response_data["vault_url"]
            vault_secret_path = response_data["vault_secret_path"].split("/data/")[1]

            vault_mount_point = config["vault"]["mount_point"]
            vault_approle_id = config["vault"]["approle_id"]
            vault_approle_secret_id = config["vault"]["approle_secret_id"]
            vault_verify = (
                True if config["vault"]["verify"] in ["True", "true"] else False
            )

            client = hvac.Client(url=vault_url, verify=vault_verify)
            client.auth.approle.login(
                role_id=vault_approle_id,
                secret_id=vault_approle_secret_id,
            )
            secrets = client.secrets.kv.v2.read_secret_version(
                mount_point=vault_mount_point,
                path=vault_secret_path,
                raise_on_deleted_version=True,
            )
            access_key = secrets["data"]["data"]["access_key"]
            secret_access_key = secrets["data"]["data"]["secret_access_key"]

            # Verify we got the required credentials
            self.assertNotEqual(access_key, "")
            self.assertNotEqual(secret_access_key, "")
            self.assertNotEqual(endpoint_url, "")

            # Track for cleanup
            self.created_accounts.append(account_name)

            # Wait for account to be fully provisioned
            time.sleep(3)

            print(f"Creating bucket: {bucket_name}")
            # Step 2: Create S3 client and bucket using the account credentials
            s3_client = boto3.client(
                "s3",
                endpoint_url=endpoint_url,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_access_key,
                region_name=self.region,
                verify=False,  # For testing with self-signed certificates
            )

            # Create bucket
            s3_client.create_bucket(Bucket=bucket_name)

            # Verify bucket was created by listing buckets
            buckets_response = s3_client.list_buckets()
            bucket_names = [bucket["Name"] for bucket in buckets_response["Buckets"]]
            self.assertIn(bucket_name, bucket_names)

            # Step 3: Try to delete account without removing buckets (should fail)
            print(f"Deleting account (expect failure): {account_name}")
            delete_params = {"region": self.region, "remove_buckets": False}
            delete_response = self.client.delete(
                f"{self.base_url}/console/account/{account_name}",
                headers=self.headers,
                params=delete_params,
            )

            # Should fail because account has buckets
            # The exact status code may vary (409 Conflict or 400 Bad Request)
            self.assertIn(
                delete_response.status_code,
                [
                    status.HTTP_400_BAD_REQUEST,
                    status.HTTP_409_CONFLICT,
                    status.HTTP_422_UNPROCESSABLE_ENTITY,
                ],
            )

            # Step 4: Clean up bucket manually
            try:
                # Delete all objects in bucket first (if any)
                objects_response = s3_client.list_objects_v2(Bucket=bucket_name)
                if "Contents" in objects_response:
                    objects_to_delete = [
                        {"Key": obj["Key"]} for obj in objects_response["Contents"]
                    ]
                    s3_client.delete_objects(
                        Bucket=bucket_name, Delete={"Objects": objects_to_delete}
                    )

                # Delete the bucket
                s3_client.delete_bucket(Bucket=bucket_name)

            except ClientError as e:
                # If bucket cleanup fails, we'll try force delete
                print(f"Warning: Manual bucket cleanup failed: {e}")

            # Step 5: Try to delete account again (should succeed now)
            print(f"Deleting account (expect success): {account_name}")
            delete_response_2 = self.client.delete(
                f"{self.base_url}/console/account/{account_name}",
                headers=self.headers,
                params=delete_params,
            )

            # Should succeed now that buckets are cleaned up
            self.assertEqual(delete_response_2.status_code, 204)

            # Remove from cleanup list since it's deleted
            if account_name in self.created_accounts:
                self.created_accounts.remove(account_name)

        except Exception as e:
            # If anything fails, try force delete with remove_buckets=True
            try:
                force_delete_params = {"region": self.region, "remove_buckets": True}
                self.client.delete(
                    f"{self.base_url}/console/account/{account_name}",
                    headers=self.headers,
                    params=force_delete_params,
                )
                if account_name in self.created_accounts:
                    self.created_accounts.remove(account_name)
            except Exception:
                pass  # Cleanup will handle it in tearDown

            self.fail(f"Account lifecycle test failed: {e}")


if __name__ == "__main__":
    unittest.main()
