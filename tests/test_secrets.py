import unittest
from unittest.mock import MagicMock, patch

from api.secrets import generate_secret_path_for_user


class TestGenerateSecretPathForUser(unittest.TestCase):
    """Unit tests for the generate_secret_path_for_user function"""

    def setUp(self):
        """Set up test fixtures"""
        self.test_username = "testuser"
        self.test_region = "fr-par"
        self.test_account_name = "test-account"
        self.test_path_prefix = "secret/data/jso"

    @patch("api.secrets.secrets.config")
    def test_force_cust_true_generates_customer_path(self, mock_config):
        """Test that force_cust=True generates customer path regardless of username"""
        # Setup mock config - config["vault"] returns a dict-like object
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = (
            self.test_path_prefix
        )  # for path_prefix
        mock_vault_section.get.return_value = "different_user"  # for si_username
        mock_config.__getitem__.return_value = mock_vault_section

        # Call function with force_cust=True
        result = generate_secret_path_for_user(
            username=self.test_username,
            region=self.test_region,
            account_name=self.test_account_name,
            force_cust=True,
        )

        # Verify customer path is generated
        expected_path = f"{self.test_path_prefix}/{self.test_region}/accounts/cust/{self.test_account_name}"
        self.assertEqual(result, expected_path)

    @patch("api.secrets.secrets.config")
    def test_force_cust_false_generates_corporate_path(self, mock_config):
        """Test that force_cust=False generates corporate path when username is not si_username"""
        # Setup mock config
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = self.test_path_prefix
        mock_vault_section.get.return_value = (
            "different_user"  # si_username is different
        )
        mock_config.__getitem__.return_value = mock_vault_section

        # Call function with force_cust=False
        result = generate_secret_path_for_user(
            username=self.test_username,
            region=self.test_region,
            account_name=self.test_account_name,
            force_cust=False,
        )

        # Verify corporate path is generated
        expected_path = f"{self.test_path_prefix}/{self.test_region}/accounts/corp/{self.test_account_name}"
        self.assertEqual(result, expected_path)

    @patch("api.secrets.secrets.config")
    def test_default_force_cust_generates_corporate_path(self, mock_config):
        """Test that default force_cust=False generates corporate path when username is not si_username"""
        # Setup mock config
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = self.test_path_prefix
        mock_vault_section.get.return_value = (
            "different_user"  # si_username is different
        )
        mock_config.__getitem__.return_value = mock_vault_section

        # Call function without force_cust parameter (defaults to False)
        result = generate_secret_path_for_user(
            username=self.test_username,
            region=self.test_region,
            account_name=self.test_account_name,
        )

        # Verify corporate path is generated
        expected_path = f"{self.test_path_prefix}/{self.test_region}/accounts/corp/{self.test_account_name}"
        self.assertEqual(result, expected_path)

    @patch("api.secrets.secrets.config")
    def test_si_username_generates_customer_path_regardless_of_force_cust(
        self, mock_config
    ):
        """Test that si_username generates customer path even when force_cust=False"""
        # Setup mock config where username matches si_username
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = self.test_path_prefix
        mock_vault_section.get.return_value = self.test_username  # si_username matches
        mock_config.__getitem__.return_value = mock_vault_section

        # Call function with force_cust=False
        result = generate_secret_path_for_user(
            username=self.test_username,
            region=self.test_region,
            account_name=self.test_account_name,
            force_cust=False,
        )

        # Verify customer path is generated (because username == si_username)
        expected_path = f"{self.test_path_prefix}/{self.test_region}/accounts/cust/{self.test_account_name}"
        self.assertEqual(result, expected_path)

    @patch("api.secrets.secrets.config")
    def test_si_username_with_force_cust_true_generates_customer_path(
        self, mock_config
    ):
        """Test that si_username with force_cust=True generates customer path"""
        # Setup mock config where username matches si_username
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = self.test_path_prefix
        mock_vault_section.get.return_value = self.test_username  # si_username matches
        mock_config.__getitem__.return_value = mock_vault_section

        # Call function with force_cust=True
        result = generate_secret_path_for_user(
            username=self.test_username,
            region=self.test_region,
            account_name=self.test_account_name,
            force_cust=True,
        )

        # Verify customer path is generated
        expected_path = f"{self.test_path_prefix}/{self.test_region}/accounts/cust/{self.test_account_name}"
        self.assertEqual(result, expected_path)

    @patch("api.secrets.secrets.config")
    def test_empty_si_username_with_force_cust_false_generates_corporate_path(
        self, mock_config
    ):
        """Test that empty si_username with force_cust=False generates corporate path"""
        # Setup mock config with empty si_username
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = self.test_path_prefix
        mock_vault_section.get.return_value = ""  # Empty si_username
        mock_config.__getitem__.return_value = mock_vault_section

        # Call function with force_cust=False
        result = generate_secret_path_for_user(
            username=self.test_username,
            region=self.test_region,
            account_name=self.test_account_name,
            force_cust=False,
        )

        # Verify corporate path is generated
        expected_path = f"{self.test_path_prefix}/{self.test_region}/accounts/corp/{self.test_account_name}"
        self.assertEqual(result, expected_path)

    @patch("api.secrets.secrets.config")
    def test_none_si_username_with_force_cust_false_generates_corporate_path(
        self, mock_config
    ):
        """Test that None si_username with force_cust=False generates corporate path"""
        # Setup mock config with None si_username
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = self.test_path_prefix
        mock_vault_section.get.return_value = None  # None si_username
        mock_config.__getitem__.return_value = mock_vault_section

        # Call function with force_cust=False
        result = generate_secret_path_for_user(
            username=self.test_username,
            region=self.test_region,
            account_name=self.test_account_name,
            force_cust=False,
        )

        # Verify corporate path is generated
        expected_path = f"{self.test_path_prefix}/{self.test_region}/accounts/corp/{self.test_account_name}"
        self.assertEqual(result, expected_path)

    @patch("api.secrets.secrets.config")
    def test_different_regions_generate_correct_paths(self, mock_config):
        """Test that different regions generate correct paths"""
        # Setup mock config
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = self.test_path_prefix
        mock_vault_section.get.return_value = "different_user"
        mock_config.__getitem__.return_value = mock_vault_section

        test_regions = ["fr-par", "fr-lyo", "us-east-1"]

        for region in test_regions:
            with self.subTest(region=region):
                result = generate_secret_path_for_user(
                    username=self.test_username,
                    region=region,
                    account_name=self.test_account_name,
                    force_cust=False,
                )

                expected_path = f"{self.test_path_prefix}/{region}/accounts/corp/{self.test_account_name}"
                self.assertEqual(result, expected_path)

    @patch("api.secrets.secrets.config")
    def test_different_account_names_generate_correct_paths(self, mock_config):
        """Test that different account names generate correct paths"""
        # Setup mock config
        mock_vault_section = MagicMock()
        mock_vault_section.__getitem__.return_value = self.test_path_prefix
        mock_vault_section.get.return_value = "different_user"
        mock_config.__getitem__.return_value = mock_vault_section

        test_account_names = ["account1", "test-account-123", "my_special_account"]

        for account_name in test_account_names:
            with self.subTest(account_name=account_name):
                result = generate_secret_path_for_user(
                    username=self.test_username,
                    region=self.test_region,
                    account_name=account_name,
                    force_cust=True,
                )

                expected_path = f"{self.test_path_prefix}/{self.test_region}/accounts/cust/{account_name}"
                self.assertEqual(result, expected_path)


if __name__ == "__main__":
    unittest.main()
