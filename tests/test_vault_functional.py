import os
import time
import unittest
import uuid
import warnings

import httpx
from dotenv import load_dotenv
from requests.packages.urllib3.exceptions import InsecureRequestWarning

warnings.simplefilter("ignore", InsecureRequestWarning)

load_dotenv()


def get_token(client_id: str, client_secret: str) -> str:
    """Get OAuth2 token for API authentication"""
    endpoint = os.getenv("IDENTITY_PROVIDER_TOKEN_URL", "")
    headers = {
        "accept": "application/json",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    payload = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret,
    }

    with httpx.Client(timeout=30.0) as client:
        response = client.post(endpoint, headers=headers, data=payload)
        response.raise_for_status()
        return response.json()["access_token"]


class TestVault(unittest.TestCase):
    """Basic vault tests for existing accounts"""

    @classmethod
    def setUpClass(cls):
        cls.url = "http://localhost:8071"
        cls.account = os.environ.get("TEST_ACCOUNT", None)
        assert cls.account is not None, "TEST_ACCOUNT not set"
        cls.account_id = os.environ.get("TEST_ACCOUNT_ID", None)
        assert cls.account_id is not None, "TEST_ACCOUNT_ID not set"
        cls.region = os.environ.get("TEST_REGION", None)
        assert cls.region is not None, "TEST_REGION not set"

        cls.clientid = os.environ.get("CLIENT_ID_JSO", None)
        assert cls.clientid is not None, "CLIENT_ID_JSO not set"
        cls.clientsecret = os.environ.get("CLIENT_SECRET_JSO", None)
        assert cls.clientsecret is not None, "CLIENT_SECRET_JSO not set"
        cls.token = get_token(cls.clientid, cls.clientsecret)
        assert cls.token is not None, "Failed to get token"

    def setUp(self):
        """Set up for each test"""
        self.headers = {"Authorization": f"Bearer {self.token}"}
        self.client = httpx.Client(timeout=60.0)

    def tearDown(self):
        """Clean up after each test"""
        self.client.close()

    def test_get_account(self):
        endpoint = f"{self.url}/vault/account/{self.account}"
        params = {"region": self.region, "starts_with": False, "ends_with": False}
        try:
            r = self.client.get(
                endpoint,
                headers=self.headers,
                params=params,
            )
            r.raise_for_status()
            self.assertEqual(r.status_code, 200)
            self.assertEqual(r.json()[0]["name"], self.account)
            self.assertEqual(r.json()[0]["id"], self.account_id)
        except Exception as e:
            raise e

    def test_get_account_cached(self):
        # First call to populate cache
        self.test_get_account()
        start_time = time.time()
        # Second call should use cache and be faster
        self.test_get_account()
        current_time = time.time()
        # Verify it was faster (cached)
        self.assertLess(current_time - start_time, 1)

    def test_get_account_startswith(self):
        endpoint = f"{self.url}/vault/account/{self.account}"
        params = {"region": self.region, "starts_with": True, "ends_with": False}
        try:
            r = self.client.get(
                endpoint,
                headers=self.headers,
                params=params,
            )
            r.raise_for_status()
            self.assertEqual(r.status_code, 200)
            self.assertEqual(r.json()[0]["name"], self.account)
            self.assertEqual(r.json()[0]["id"], self.account_id)
        except Exception as e:
            raise e

    def test_get_account_endswith(self):
        endpoint = f"{self.url}/vault/account/{self.account}"
        params = {"region": self.region, "starts_with": False, "ends_with": True}
        try:
            r = self.client.get(
                endpoint,
                headers=self.headers,
                params=params,
            )
            r.raise_for_status()
            self.assertEqual(r.status_code, 200)
            self.assertEqual(r.json()[0]["name"], self.account)
            self.assertEqual(r.json()[0]["id"], self.account_id)
        except Exception as e:
            raise e


class TestVaultFunctional(unittest.TestCase):
    """Functional test suite for vault endpoints"""

    @classmethod
    def setUpClass(cls):
        """Set up test environment and authentication"""
        cls.base_url = os.getenv("JSO_API_URL", "http://localhost:8071")
        cls.region = os.getenv("TEST_REGION", "fr-par")

        # Authentication setup
        cls.client_id = os.environ.get("CLIENT_ID_JSO")
        assert cls.client_id is not None, "CLIENT_ID_JSO environment variable not set"

        cls.client_secret = os.environ.get("CLIENT_SECRET_JSO")
        assert (
            cls.client_secret is not None
        ), "CLIENT_SECRET_JSO environment variable not set"

        # Get authentication token
        cls.token = get_token(cls.client_id, cls.client_secret)
        assert cls.token is not None, "Failed to get authentication token"

        # Test account names (using UUID to avoid conflicts)
        cls.test_account_prefix = f"test-vault-{uuid.uuid4().hex[:8]}"
        cls.created_accounts = []  # Track created accounts for cleanup

    @classmethod
    def tearDownClass(cls):
        """Clean up any remaining test accounts"""
        for account_name in cls.created_accounts:
            try:
                cls._delete_account_if_exists(account_name)
            except Exception as e:
                print(f"Warning: Failed to cleanup account {account_name}: {e}")

    @classmethod
    def _delete_account_if_exists(cls, account_name: str):
        """Helper method to delete account if it exists"""
        headers = {"Authorization": f"Bearer {cls.token}"}
        params = {"region": cls.region, "remove_buckets": True}

        with httpx.Client(timeout=60.0) as client:
            response = client.delete(
                f"{cls.base_url}/vault/account/{account_name}",
                headers=headers,
                params=params,
            )
            # Don't raise for 404 (account doesn't exist)
            if response.status_code not in [204, 404]:
                response.raise_for_status()

    def setUp(self):
        """Set up for each test"""
        self.headers = {"Authorization": f"Bearer {self.token}"}
        self.client = httpx.Client(timeout=60.0)

    def tearDown(self):
        """Clean up after each test"""
        self.client.close()

    def _create_test_account(
        self, account_name: str, quota_bytes: int = **********
    ) -> dict:
        """Helper method to create a test account via console endpoint"""
        payload = {
            "accountName": account_name,
            "console": False,  # Create vault-only account
            "quota": quota_bytes,  # 1GB in bytes
        }
        params = {"region": self.region}

        response = self.client.post(
            f"{self.base_url}/console/account",
            json=payload,
            headers=self.headers,
            params=params,
        )
        response.raise_for_status()
        self.created_accounts.append(account_name)
        return response.json()

    def test_get_vault_account_success(self):
        """Test GetVaultAccount endpoint with existing account"""
        # Create a test account first
        account_name = f"{self.test_account_prefix}-get-success"
        account_data = self._create_test_account(account_name)

        # Wait for account to be fully created
        time.sleep(2)

        # Test GetVaultAccount
        response = self.client.get(
            f"{self.base_url}/vault/account/{account_name}",
            headers=self.headers,
            params={"region": self.region},
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        # Verify response structure
        self.assertIsInstance(response_data, list)
        self.assertGreater(len(response_data), 0)

        account = response_data[0]
        self.assertIn("arn", account)
        self.assertIn("id", account)
        self.assertIn("name", account)
        self.assertIn("createDate", account)
        self.assertIn("emailAddress", account)
        self.assertIn("canonicalId", account)
        self.assertIn("quota", account)

        # Verify account details
        self.assertEqual(account["name"], account_name)
        self.assertEqual(account["id"], account_data["accountId"])

    def test_get_vault_account_with_prefix(self):
        """Test GetVaultAccount with starts_with parameter"""
        account_name = f"{self.test_account_prefix}-prefix-test"
        self._create_test_account(account_name)
        time.sleep(2)

        # Test with prefix search
        response = self.client.get(
            f"{self.base_url}/vault/account/{self.test_account_prefix}",
            headers=self.headers,
            params={"region": self.region, "starts_with": True},
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        # Should find at least our test account
        self.assertIsInstance(response_data, list)
        account_names = [acc["name"] for acc in response_data]
        self.assertIn(account_name, account_names)

    def test_get_vault_account_with_suffix(self):
        """Test GetVaultAccount with ends_with parameter"""
        account_name = f"{self.test_account_prefix}-suffix-test"
        self._create_test_account(account_name)
        time.sleep(2)

        # Test with suffix search
        response = self.client.get(
            f"{self.base_url}/vault/account/suffix-test",
            headers=self.headers,
            params={"region": self.region, "ends_with": True},
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        # Should find our test account
        self.assertIsInstance(response_data, list)
        account_names = [acc["name"] for acc in response_data]
        self.assertIn(account_name, account_names)

    def test_get_vault_account_not_found(self):
        """Test GetVaultAccount with non-existent account"""
        nonexistent_account = f"{self.test_account_prefix}-nonexistent"

        response = self.client.get(
            f"{self.base_url}/vault/account/{nonexistent_account}",
            headers=self.headers,
            params={"region": self.region},
        )

        self.assertEqual(response.status_code, 404)

    def test_get_vault_account_unauthorized(self):
        """Test GetVaultAccount without proper authentication"""
        account_name = f"{self.test_account_prefix}-unauthorized"

        response = self.client.get(
            f"{self.base_url}/vault/account/{account_name}",
            params={"region": self.region},
        )

        # Should return 401 Unauthorized or 403 Forbidden
        self.assertIn(response.status_code, [401, 403])

    def test_delete_vault_account_success(self):
        """Test DeleteVaultAccount endpoint"""
        account_name = f"{self.test_account_prefix}-delete-success"
        self._create_test_account(account_name)
        time.sleep(2)

        # Delete the account
        response = self.client.delete(
            f"{self.base_url}/vault/account/{account_name}",
            headers=self.headers,
            params={"region": self.region, "remove_buckets": False},
        )

        self.assertEqual(response.status_code, 204)

        # Remove from cleanup list since it's deleted
        if account_name in self.created_accounts:
            self.created_accounts.remove(account_name)

        # Verify account is deleted by trying to get it
        get_response = self.client.get(
            f"{self.base_url}/vault/account/{account_name}",
            headers=self.headers,
            params={"region": self.region},
        )
        self.assertEqual(get_response.status_code, 404)

    def test_delete_vault_account_not_found(self):
        """Test DeleteVaultAccount with non-existent account"""
        nonexistent_account = f"{self.test_account_prefix}-delete-nonexistent"

        response = self.client.delete(
            f"{self.base_url}/vault/account/{nonexistent_account}",
            headers=self.headers,
            params={"region": self.region, "remove_buckets": False},
        )

        self.assertEqual(response.status_code, 404)

    def test_delete_vault_account_with_force_bucket_removal(self):
        """Test DeleteVaultAccount with remove_buckets=True"""
        account_name = f"{self.test_account_prefix}-delete-force"
        self._create_test_account(account_name)
        time.sleep(2)

        # Delete with force bucket removal
        response = self.client.delete(
            f"{self.base_url}/vault/account/{account_name}",
            headers=self.headers,
            params={"region": self.region, "remove_buckets": True},
        )

        self.assertEqual(response.status_code, 204)

        # Remove from cleanup list since it's deleted
        if account_name in self.created_accounts:
            self.created_accounts.remove(account_name)

    def test_get_vault_account_quota_success(self):
        """Test GetVaultAccountQuota endpoint"""
        account_name = f"{self.test_account_prefix}-quota-get"
        self._create_test_account(account_name, quota_bytes=**********)
        time.sleep(2)

        response = self.client.get(
            f"{self.base_url}/vault/account/{account_name}/quota",
            headers=self.headers,
            params={"region": self.region},
        )

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        # Verify response structure
        self.assertIn("quota", response_data)
        self.assertIsInstance(response_data["quota"], int)
        self.assertEqual(response_data["quota"], **********)  # 1GB in bytes

        # Optional fields that may or may not be present
        if "canonicalId" in response_data:
            self.assertIsInstance(response_data["canonicalId"], (str, type(None)))
        if "arn" in response_data:
            self.assertIsInstance(response_data["arn"], (str, type(None)))

    def test_get_vault_account_quota_not_found(self):
        """Test GetVaultAccountQuota with non-existent account"""
        nonexistent_account = f"{self.test_account_prefix}-quota-nonexistent"

        response = self.client.get(
            f"{self.base_url}/vault/account/{nonexistent_account}/quota",
            headers=self.headers,
            params={"region": self.region},
        )

        self.assertEqual(response.status_code, 404)

    def test_update_vault_account_quota_success(self):
        """Test UpdateVaultAccountQuota endpoint"""
        account_name = f"{self.test_account_prefix}-quota-update"
        self._create_test_account(account_name)
        time.sleep(2)

        # Update quota to 2GB
        new_quota = **********  # 2GB in bytes
        response = self.client.put(
            f"{self.base_url}/vault/account/{account_name}/quota",
            headers=self.headers,
            params={"region": self.region, "quota_bytes": new_quota},
        )

        self.assertEqual(response.status_code, 204)

        # Verify quota was updated by getting it
        get_response = self.client.get(
            f"{self.base_url}/vault/account/{account_name}/quota",
            headers=self.headers,
            params={"region": self.region},
        )

        self.assertEqual(get_response.status_code, 200)
        quota_data = get_response.json()
        self.assertEqual(quota_data["quota"], new_quota)

    def test_update_vault_account_quota_not_found(self):
        """Test UpdateVaultAccountQuota with non-existent account"""
        nonexistent_account = f"{self.test_account_prefix}-quota-update-nonexistent"

        response = self.client.put(
            f"{self.base_url}/vault/account/{nonexistent_account}/quota",
            headers=self.headers,
            params={"region": self.region, "quota_bytes": **********},
        )

        self.assertEqual(response.status_code, 404)

    def test_update_vault_account_quota_invalid_value(self):
        """Test UpdateVaultAccountQuota with invalid quota value"""
        account_name = f"{self.test_account_prefix}-quota-invalid"
        self._create_test_account(account_name)
        time.sleep(2)

        # Try to set negative quota
        response = self.client.put(
            f"{self.base_url}/vault/account/{account_name}/quota",
            headers=self.headers,
            params={"region": self.region, "quota_bytes": -1},
        )

        # Should return 422 for validation error
        self.assertEqual(response.status_code, 422)

    def test_delete_vault_account_quota_success(self):
        """Test DeleteVaultAccountQuota endpoint"""
        account_name = f"{self.test_account_prefix}-quota-delete"
        self._create_test_account(account_name)
        time.sleep(2)

        # First set a quota
        self.client.put(
            f"{self.base_url}/vault/account/{account_name}/quota",
            headers=self.headers,
            params={"region": self.region, "quota_bytes": **********},
        )

        # Delete the quota
        response = self.client.delete(
            f"{self.base_url}/vault/account/{account_name}/quota",
            headers=self.headers,
            params={"region": self.region},
        )

        self.assertEqual(response.status_code, 204)

        # Verify quota was deleted/reset by getting it
        get_response = self.client.get(
            f"{self.base_url}/vault/account/{account_name}/quota",
            headers=self.headers,
            params={"region": self.region},
        )

        self.assertEqual(get_response.status_code, 200)
        quota_data = get_response.json()
        # After deletion, quota should be 0 or default value
        self.assertGreaterEqual(quota_data["quota"], 0)

    def test_delete_vault_account_quota_not_found(self):
        """Test DeleteVaultAccountQuota with non-existent account"""
        nonexistent_account = f"{self.test_account_prefix}-quota-delete-nonexistent"

        response = self.client.delete(
            f"{self.base_url}/vault/account/{nonexistent_account}/quota",
            headers=self.headers,
            params={"region": self.region},
        )

        self.assertEqual(response.status_code, 404)

    def test_vault_endpoints_unauthorized(self):
        """Test that all vault endpoints require proper authentication"""
        account_name = f"{self.test_account_prefix}-auth-test"

        # Test all endpoints without authorization header
        endpoints_and_methods = [
            ("GET", f"/vault/account/{account_name}"),
            ("DELETE", f"/vault/account/{account_name}"),
            ("GET", f"/vault/account/{account_name}/quota"),
            ("PUT", f"/vault/account/{account_name}/quota"),
            ("DELETE", f"/vault/account/{account_name}/quota"),
        ]

        for method, endpoint in endpoints_and_methods:
            with self.subTest(method=method, endpoint=endpoint):
                params = {"region": self.region}
                if method == "PUT":
                    params["quota_bytes"] = "**********"

                response = self.client.request(
                    method,
                    f"{self.base_url}{endpoint}",
                    params=params,
                )

                # Should return 401 Unauthorized or 403 Forbidden
                self.assertIn(response.status_code, [401, 403])

    def test_vault_endpoints_invalid_region(self):
        """Test vault endpoints with invalid region"""
        account_name = f"{self.test_account_prefix}-invalid-region"

        endpoints_and_methods = [
            ("GET", f"/vault/account/{account_name}"),
            ("DELETE", f"/vault/account/{account_name}"),
            ("GET", f"/vault/account/{account_name}/quota"),
            ("PUT", f"/vault/account/{account_name}/quota"),
            ("DELETE", f"/vault/account/{account_name}/quota"),
        ]

        for method, endpoint in endpoints_and_methods:
            with self.subTest(method=method, endpoint=endpoint):
                params = {"region": "invalid-region"}
                if method == "PUT":
                    params["quota_bytes"] = "**********"

                response = self.client.request(
                    method,
                    f"{self.base_url}{endpoint}",
                    headers=self.headers,
                    params=params,
                )

                # Should return 422 for invalid region enum value
                self.assertEqual(response.status_code, 422)

    def test_vault_account_lifecycle(self):
        """Test complete vault account lifecycle: create, get, update quota, delete quota, delete account"""
        account_name = f"{self.test_account_prefix}-lifecycle"

        try:
            # Step 1: Create account via console endpoint
            print(f"Creating account: {account_name}")
            account_data = self._create_test_account(account_name)
            time.sleep(2)

            # Step 2: Get account via vault endpoint
            print(f"Getting account: {account_name}")
            get_response = self.client.get(
                f"{self.base_url}/vault/account/{account_name}",
                headers=self.headers,
                params={"region": self.region},
            )
            self.assertEqual(get_response.status_code, 200)
            vault_account = get_response.json()[0]
            self.assertEqual(vault_account["name"], account_name)
            self.assertEqual(vault_account["id"], account_data["accountId"])

            # Step 3: Get initial quota
            print(f"Getting initial quota for: {account_name}")
            quota_response = self.client.get(
                f"{self.base_url}/vault/account/{account_name}/quota",
                headers=self.headers,
                params={"region": self.region},
            )
            self.assertEqual(quota_response.status_code, 200)
            initial_quota = quota_response.json()["quota"]
            self.assertGreaterEqual(initial_quota, 0)  # Verify initial quota is valid

            # Step 4: Update quota
            print(f"Updating quota for: {account_name}")
            new_quota = **********  # 2GB
            update_response = self.client.put(
                f"{self.base_url}/vault/account/{account_name}/quota",
                headers=self.headers,
                params={"region": self.region, "quota_bytes": new_quota},
            )
            self.assertEqual(update_response.status_code, 204)

            # Step 5: Verify quota was updated
            print(f"Verifying quota update for: {account_name}")
            updated_quota_response = self.client.get(
                f"{self.base_url}/vault/account/{account_name}/quota",
                headers=self.headers,
                params={"region": self.region},
            )
            self.assertEqual(updated_quota_response.status_code, 200)
            self.assertEqual(updated_quota_response.json()["quota"], new_quota)

            # Step 6: Delete quota
            print(f"Deleting quota for: {account_name}")
            delete_quota_response = self.client.delete(
                f"{self.base_url}/vault/account/{account_name}/quota",
                headers=self.headers,
                params={"region": self.region},
            )
            self.assertEqual(delete_quota_response.status_code, 204)

            # Step 7: Verify quota was deleted/reset
            print(f"Verifying quota deletion for: {account_name}")
            final_quota_response = self.client.get(
                f"{self.base_url}/vault/account/{account_name}/quota",
                headers=self.headers,
                params={"region": self.region},
            )
            self.assertEqual(final_quota_response.status_code, 200)
            final_quota = final_quota_response.json()["quota"]
            self.assertGreaterEqual(final_quota, 0)

            # Step 8: Delete account
            print(f"Deleting account: {account_name}")
            delete_response = self.client.delete(
                f"{self.base_url}/vault/account/{account_name}",
                headers=self.headers,
                params={"region": self.region, "remove_buckets": True},
            )
            self.assertEqual(delete_response.status_code, 204)

            # Remove from cleanup list since it's deleted
            if account_name in self.created_accounts:
                self.created_accounts.remove(account_name)

            # Step 9: Verify account is deleted
            print(f"Verifying account deletion: {account_name}")
            final_get_response = self.client.get(
                f"{self.base_url}/vault/account/{account_name}",
                headers=self.headers,
                params={"region": self.region},
            )
            print(final_get_response.json())
            self.assertEqual(final_get_response.status_code, 404)

            print(f"Lifecycle test completed successfully for: {account_name}")

        except Exception as e:
            # If anything fails, try cleanup
            try:
                self.client.delete(
                    f"{self.base_url}/vault/account/{account_name}",
                    headers=self.headers,
                    params={"region": self.region, "remove_buckets": True},
                )
                if account_name in self.created_accounts:
                    self.created_accounts.remove(account_name)
            except Exception:
                pass  # Cleanup will handle it in tearDown

            self.fail(f"Vault account lifecycle test failed: {e}")


if __name__ == "__main__":
    unittest.main()
