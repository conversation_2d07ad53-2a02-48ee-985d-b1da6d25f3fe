import logging

from fastapi import (  # type: ignore
    APIRouter,
    Depends,
    Path,
    status,
)
from fastapi.responses import JSONResponse  # type: ignore
from fp_aaa_kit import (
    FPAuditLogEntryEvent,
    FPUser,
    validate_user,
)
from fp_aaa_kit.middleware import (
    AuditedSection,
    prepare_audited_section,
)
from jnapi_async import Jobs, Session, Settings
from jnapi_async import exceptions as JNAPI_exceptions
from jnapi_async.Module import ResponseMessage  # type: ignore  # type: ignore

from ..config import Config
from ..models import (
    JobId,
    JsoObjectsCleanupInfo,
    JsoObjectsCleanupInstance,
)
from ..queues_names import JSO_OBJECTS_CLEANUP_QUEUE
from . import responses

logging.basicConfig(format="{levelname:7} {message}", style="{", level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

config = Config().get_config_parser()
router = APIRouter(prefix="/bucket", redirect_slashes=False, tags=["cleanup"])

settings = Settings()  # type: ignore

session = Session(
    gateway_api_url=settings.GATEWAY_API_URL,
    identity_provider_url=settings.IDENTITY_PROVIDER_URL,  # type: ignore
    realm=settings.IDENTITY_PROVIDER_REALM,  # type: ignore
    client_id=settings.CLIENT_ID,
    client_secret=settings.CLIENT_SECRET,
)


@router.post(
    "/{bucket}/objects/delete",
    summary="ObjectsCleanup",
    responses={
        500: {"model": dict},
        423: {"model": dict},
        404: {"model": dict},
        201: {"model": JobId},
    },
    status_code=201,
)
async def ObjectsCleanup(
    instance: JsoObjectsCleanupInstance,
    bucket: str = Path(..., title="The reference of the bucket to cleanup"),
    user: FPUser = Depends(validate_user(None)),
    audited_section: AuditedSection = Depends(prepare_audited_section()),
):
    """
    Empty a bucket (Async job)
    """

    data = instance.dict()
    data["bucket"] = bucket
    service_id = "jso_" + bucket
    try:
        data["endpoint_url"] = config[data["region"]]["s3_endpoint_url"]
    except Exception:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage("Unknown region").dict(),
        )

    job = Jobs(session=session)
    try:
        with audited_section(
            action="cleanup",
            kind="bucket",
            what=bucket,
            default_event=FPAuditLogEntryEvent.ALLOWED,
        ):
            job_id = await job.create(
                queue=JSO_OBJECTS_CLEANUP_QUEUE,
                service_id=service_id,
                data=data,
            )
    except JNAPI_exceptions.ServiceLockedError:
        logger.exception(f"Service {service_id}: locked")
        return JSONResponse(
            status_code=status.HTTP_423_LOCKED,
            content=ResponseMessage(f"Service {service_id}: locked").dict(),
        )
    except Exception as e:
        logger.exception("Failed to create job")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage(str(e)).dict(),
        )

    # Return the job_id as a JSON response.
    return JSONResponse(
        status_code=status.HTTP_201_CREATED, content=JobId(job_id).dict()
    )


@router.get(
    "/{bucket}/objects/delete",
    summary="ObjectsCleanupInfo",
    responses={**responses, 200: {"model": JsoObjectsCleanupInfo}},
    status_code=200,
)
async def ObjectsCleanupInfo(
    job_id: int,
    bucket: str = Path(..., title="The reference of the bucket to request"),
):
    """
    Get empty bucket status
    """

    if job_id <= 0:
        return JSONResponse(
            status_code=404,
            content=ResponseMessage(str("Invalid job id")).dict(),
        )

    try:
        jobs = Jobs(session=session)
        job = await jobs.get_job(job_id)

        if job.queue == JSO_OBJECTS_CLEANUP_QUEUE:
            assert bucket == job.body["bucket"]

            if job.logs == []:
                return JSONResponse(
                    status_code=status.HTTP_200_OK,
                    content=JsoObjectsCleanupInfo(
                        job.service_id,  # type: ignore
                        bucket,
                        job.state.value,
                        {},  # type: ignore
                    ).dict(),
                )
    except Exception as e:
        logger.exception(f"Failed to get cleanup job: {job_id}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get job {job_id}: {str(e)}")
            ).dict(),
        )

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=JsoObjectsCleanupInfo(
            job.service_id,  # type: ignore
            bucket,
            job.state.value,
            job.logs[-1],  # type: ignore
        ).dict(),
    )
