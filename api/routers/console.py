import logging
import secrets

from fastapi import (  # type: ignore
    APIRouter,
    Depends,
    HTTPException,
    Path,
    Query,
    Response,
    status,
)
from fastapi.responses import JSONResponse  # type: ignore
from fp_aaa_kit import (
    AnyRoleIn,  # type: ignore
    FPAuditLogEntryEvent,
    FPUser,
    validate_user,
)
from fp_aaa_kit.middleware import (
    AuditedSection,
    prepare_audited_section,
)
from jnapi_async.Module import ResponseMessage  # type: ignore

from ..config import Config, Regions
from ..exceptions import AccessDenied, EntityAlreadyExists, NoSuchEntity
from ..models import (
    AccountInstanceInputs,
    AccountInstanceResponse,
    ConsoleAccountResponse,
)
from ..providers import ProviderFactory
from ..secrets import (
    SecretsClient,
    generate_secret_path_for_user,
    generate_shared_secret,
)
from ..utils import Cache
from . import responses

logging.basicConfig(format="{levelname:7} {message}", style="{", level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

config = Config().get_config_parser()
router = APIRouter(prefix="/console", redirect_slashes=False, tags=["console"])

cache = Cache()


def get_console_provider(
    region: Regions = Query(..., title="S3 Region"),
):
    """Get console provider for the specified region"""
    try:
        return ProviderFactory.get_console_provider(region)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Region metadata not found"
        )


def get_account_provider(
    region: Regions = Query(..., title="S3 Region"),
):
    """Get account provider for the specified region"""
    try:
        return ProviderFactory.get_account_provider(region)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Region metadata not found"
        )


@router.get(
    "/account/{account_name}",
    summary="GetConsoleAccount",
    responses={**responses, 200: {"model": ConsoleAccountResponse}},
    status_code=200,
)
async def GetConsoleAccount(
    console_provider=Depends(get_console_provider),
    account_name: str = Path(..., title="Account name"),
    region: Regions = Query(..., title="S3 Region"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_read"))),
):
    key = {"account_name": account_name, "region": region}
    if cache.hit(key):
        return JSONResponse(status_code=status.HTTP_200_OK, content=cache.get(key))

    try:
        account = await console_provider.get_account(account_name)
        cache.set(key, account)
    except AccessDenied as e:
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=ResponseMessage(str(e)).dict(),
        )
    except NoSuchEntity as e:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to get account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(f"Failed to get account '{account_name}': {str(e)}")
            ).dict(),
        )

    return JSONResponse(status_code=status.HTTP_200_OK, content=account)


@router.post(
    "/account",
    summary="CreateConsoleAccount",
    responses={**responses, 201: {"model": dict}},
    status_code=201,
    response_model=AccountInstanceResponse,
)
async def CreateConsoleAccount(
    instance: AccountInstanceInputs,
    region: Regions = Query(..., title="S3 Region"),
    force_cust: bool = Query(default=False, title="Force customer account creation"),
    user: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
    audited_section: AuditedSection = Depends(prepare_audited_section()),
):
    data = instance.dict()

    # Get providers
    console_provider = ProviderFactory.get_console_provider(region)
    account_provider = ProviderFactory.get_account_provider(region)

    account_password = secrets.token_urlsafe(15) + "@"
    account_name = data["accountName"]
    account_id = data["accountId"]
    account_console = data["console"]
    account_quota = data["quota"]
    account_email = account_name + "@freepro.com"

    try:
        with audited_section(
            action="create",
            kind="account",
            what=instance.accountName,
            default_event=FPAuditLogEntryEvent.ALLOWED,
        ):
            if account_console is True:
                resp = await console_provider.create_account(
                    account_name=account_name,
                    email=account_email,
                    quota=account_quota,
                    password=account_password,
                )

                accessKey = resp.accessKey
                secretAccessKey = resp.secretAccessKey

                # due to a bug in with the Scality Console API, regenerate superadmin access keys
                if console_provider.provider == "scality":
                    root_user_keys = await account_provider.generate_account_access_key(
                        account_name=account_name
                    )
                    accessKey = root_user_keys["accessKey"]
                    secretAccessKey = root_user_keys["secretKeyValue"]

            else:
                resp = await account_provider.create_account(
                    account_name=account_name,
                    account_id=account_id,  # some provider require an accountID
                    email=account_email,
                    quota=account_quota,
                )
                accessKey = resp.accessKey
                secretAccessKey = resp.secretAccessKey
                account_password = ""

        vault_secret_saved = False
        try:
            with SecretsClient(
                url=config["vault"]["url"],
                mount_point=config["vault"]["mount_point"],
                approle_id=config["vault"]["approle_id"],
                approle_secret_id=config["vault"]["approle_secret_id"],
                verify=config["vault"]["verify"],
            ) as client:
                secret_path = generate_secret_path_for_user(
                    username=user.username,
                    region=region,
                    account_name=account_name,
                    force_cust=force_cust,
                )
                client.save_secrets(
                    path=secret_path,
                    secrets={
                        "account_id": resp.accountId,
                        "console_login": account_name,
                        "console_password": account_password,
                        "access_key": accessKey,
                        "secret_access_key": secretAccessKey,
                    },
                )
                vault_secret_saved = True
        except Exception:
            logger.exception(f"Failed to save account secrets for '{account_name}'")

        shared_secret_url = generate_shared_secret(
            secret=f"""
Your service account {account_name} has been created.

endpoint_url: {config[region]["s3_endpoint_url"]}
console_login: {account_name}
console_password: {account_password}
access_key: {accessKey}
secret_access_key: {secretAccessKey}"""
        )

        if shared_secret_url is None:
            logger.error(f"Failed to generate shared secret for '{account_name}'")
            shared_secret_url = (
                "Failed to generate shared secret, contact your Administrators"
            )

        if vault_secret_saved:
            # if secret are saved in Vault, do not return the secret keys
            account_info = AccountInstanceResponse(
                endpointUrl=config[region]["s3_endpoint_url"],
                accountId=resp.accountId,
                accountName=resp.accountName,
                emailAddress=resp.emailAddress,
                consolePassword="",
                accessKey="",
                secretAccessKey="",
                shared_secret_url=shared_secret_url,  # type: ignore
                vault_server_url=config["vault"]["url"],
                vault_api_path=f"/v1/{config['vault']['mount_point']}/data/{secret_path}",
                vault_ui_path=f"{config['vault']['url']}/ui/vault/secrets/sys-kv-1/kv/{secret_path}/details?version=1",
            )
        else:
            account_info = AccountInstanceResponse(
                endpointUrl=config[region]["s3_endpoint_url"],
                accountId=resp.accountId,
                accountName=resp.accountName,
                emailAddress=resp.emailAddress,
                consolePassword=account_password,
                accessKey=accessKey,
                secretAccessKey=secretAccessKey,
                shared_secret_url=shared_secret_url,  # type: ignore
            )
    except AccessDenied as e:
        logging.error(str(e))
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=ResponseMessage(str(e)).dict(),
        )
    except EntityAlreadyExists as e:
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to create new account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(f"Failed to create new account '{account_name}': {str(e)}")
            ).dict(),
        )

    # snoop.pp(account_info.dict())

    return JSONResponse(
        status_code=status.HTTP_201_CREATED, content=account_info.dict()
    )


@router.delete(
    "/account/{account_name}",
    summary="DeleteConsoleAccount",
    responses={**responses, 204: {"model": None}},
    status_code=status.HTTP_204_NO_CONTENT,
)
async def DeleteConsoleAccount(
    remove_buckets: bool = Query(default=False, title="Force empty bucket removal"),
    region: Regions = Query(..., title="S3 Region"),
    force_cust: bool = Query(default=False, title="Force customer account type"),
    account_name: str = Path(..., title="Account name to delete"),
    user: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
    audited_section: AuditedSection = Depends(prepare_audited_section()),
):
    # Get providers
    account_provider = ProviderFactory.get_account_provider(region)
    console_provider = ProviderFactory.get_console_provider(region)
    s3_provider = ProviderFactory.get_s3_provider(region)

    try:
        # Generate account access keys
        root_keys = await account_provider.generate_account_access_key(account_name)

        # Get S3 client with account credentials
        s3_client = s3_provider.get_s3_client(
            access_key=root_keys["accessKey"], secret_key=root_keys["secretKeyValue"]
        )

        # Check if any bucket exists
        response = s3_client.list_buckets()
        if len(response["Buckets"]) > 0:
            if remove_buckets is False:
                return JSONResponse(
                    status_code=status.HTTP_409_CONFLICT,
                    content=ResponseMessage(
                        "Buckets exist under this account, please remove the buckets first"
                    ).dict(),
                )
            else:
                for bucket in response["Buckets"]:
                    s3_client.delete_bucket(Bucket=bucket["Name"])

        # Create IAM provider with account credentials and clean up IAM entities
        iam_provider = ProviderFactory.get_iam_provider(
            region=region,
            endpoint_url=f"https://{account_provider.config['iam_host']}:{account_provider.config['iam_port']}",
            access_key=root_keys["accessKey"],
            secret_access_key=root_keys["secretKeyValue"],
        )
        await iam_provider.cleanup_account_iam(
            access_key=root_keys["accessKey"], secret_key=root_keys["secretKeyValue"]
        )

        await account_provider.delete_account_access_key(account_name=account_name)
    except NoSuchEntity as e:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to delete account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(f"Failed to delete account '{account_name}': {str(e)}")
            ).dict(),
        )

    try:
        with audited_section(
            action="delete",
            kind="account",
            what=account_name,
            default_event=FPAuditLogEntryEvent.ALLOWED,
        ):
            await console_provider.delete_account(account_name)

    except AccessDenied as e:
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=ResponseMessage(str(e)).dict(),
        )
    except NoSuchEntity as e:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to delete account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(f"Failed to delete account '{account_name}': {str(e)}")
            ).dict(),
        )

    try:
        with SecretsClient(
            url=config["vault"]["url"],
            mount_point=config["vault"]["mount_point"],
            approle_id=config["vault"]["approle_id"],
            approle_secret_id=config["vault"]["approle_secret_id"],
            verify=config["vault"]["verify"],
        ) as client:
            secret_path = generate_secret_path_for_user(
                username=user.username,
                region=region,
                account_name=account_name,
                force_cust=force_cust,
            )
            client.destroy_secrets(secret_path)
    except Exception as e:
        logger.error(f"Failed to delete account secrets for '{account_name}': {e}")

    cache.rem({"account_name": account_name, "region": region})

    return Response(status_code=status.HTTP_204_NO_CONTENT)
