"""
Scality provider implementation that wraps existing Scality-specific services.
"""

import logging
import time
from typing import Any, Dict, List, Optional

import boto3
from scality.exceptions import NoSuchEntity as ScalityNoSuchEntity  # type: ignore
from scality.vault import VaultClient  # type: ignore

from ..exceptions import NoSuchEntity
from ..models import AccountInstanceResponse
from .base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
    Instance,
)
from .scality.s3_console import Console
from .scality.s3_iam import Iam
from .scality.s3_utapi import Instance as UtapiInstance
from .scality.s3_utapi import Utapi

logger = logging.getLogger(__name__)


class ScalityAccountProvider(BaseAccountProvider):
    """Scality implementation of account provider using VaultClient"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.vault_client = VaultClient(
            host=config["vault_host"],
            port=int(config["vault_port"]),
            use_https=True,
            access_key=config["vault_access_key"],
            secret_access_key=config["vault_secret_key"],
        )

    async def create_account(
        self, account_name: str, account_id: Optional[str], email: str, quota: int
    ) -> AccountInstanceResponse:
        """Create account using VaultClient"""
        logger.info(f"Creating Scality account: {account_name}")

        r = self.vault_client.create_account(
            name=account_name,
            email=email,
            quota=quota,
        )

        response: AccountInstanceResponse = AccountInstanceResponse(
            endpointUrl=self.config["s3_endpoint_url"],
            accountId=str(r["arn"].split(":")[4]),
            accountName=account_name,
            emailAddress=email,
            consolePassword="",
            accessKey=r.get("accessKey", ""),
            secretAccessKey=r.get("secretAccessKey", ""),
        )

        return response

    async def delete_account(self, account_name: str) -> None:
        """Delete account using VaultClient"""
        logger.info(f"Deleting Scality account: {account_name}")

        self.vault_client.delete_account(name=account_name)

    async def get_account(
        self, account_name: str, starts_with: bool = False, ends_with: bool = False
    ) -> List[Dict[str, Any]]:
        """Get account using VaultClient"""
        logger.info(f"Getting Scality account: {account_name}")

        accounts_list = []
        marker = ""
        start_time = time.time()
        count = 0
        while True:
            resp = self.vault_client.list_accounts(marker=marker)
            accounts = resp["accounts"]
            count += len(accounts)

            if not starts_with and not ends_with:
                # Direct match search
                account = next(
                    (
                        account
                        for account in accounts
                        if account["name"] == account_name
                    ),
                    None,
                )  # type: ignore

                if account:
                    accounts_list.append(account)
                    break

            if starts_with or ends_with:
                # Filter based search
                filter_conditions = {
                    (True, True): lambda x: x["name"].startswith(account_name)
                    and x["name"].endswith(account_name),
                    (True, False): lambda x: x["name"].startswith(account_name),
                    (False, True): lambda x: x["name"].endswith(account_name),
                }
                filter_func = filter_conditions.get((starts_with, ends_with))
                if filter_func:
                    filtered_accounts = filter(filter_func, accounts)
                    accounts_list.extend(list(filtered_accounts))

            # Handle pagination and completion
            if not resp["isTruncated"]:
                if len(accounts_list) == 0:
                    raise NoSuchEntity("Missing account")
                break
            marker = resp["marker"]  # paginates

        current_time = time.time()
        logger.debug(
            f"Processed {count} accounts in {str(current_time - start_time)} secs"
        )

        return accounts_list

    async def generate_account_access_key(self, account_name: str) -> Dict[str, str]:
        """Generate access key using VaultClient"""
        logger.info(f"Generating access key for Scality account: {account_name}")

        try:
            response = self.vault_client.generate_account_access_key(account_name)
        except ScalityNoSuchEntity as e:
            raise NoSuchEntity(str(e))

        return response

    async def delete_account_access_key(self, account_name: str) -> None:
        pass

    async def update_account_quota(self, account_name: str, quota_bytes: int) -> None:
        """Update account quota using VaultClient"""
        logger.info(f"Updating quota for Scality account: {account_name}")

        try:
            self.vault_client.update_account_quota(name=account_name, quota=quota_bytes)
        except ScalityNoSuchEntity as e:
            raise NoSuchEntity(str(e))

    async def delete_account_quota(self, account_name: str) -> None:
        """Delete account quota using VaultClient"""
        logger.info(f"Deleting quota for Scality account: {account_name}")

        try:
            self.vault_client.delete_account_quota(name=account_name)  # type: ignore
        except ScalityNoSuchEntity as e:
            raise NoSuchEntity(str(e))

    async def get_account_quota(self, account_name: str) -> Dict[str, Any]:
        """Get account quota using VaultClient"""
        logger.info(f"Getting quota for Scality account: {account_name}")

        try:
            response = self.vault_client.get_account_quota(name=account_name)  # type: ignore
        except ScalityNoSuchEntity as e:
            raise NoSuchEntity(str(e))

        return response


class ScalityMetricsProvider(BaseMetricsProvider):
    """Scality implementation of metrics provider using Utapi"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.utapi = Utapi(region)
        # Mapping from our Instance enum to Utapi's Instance enum
        self._instance_mapping = {
            Instance.ACCOUNT: UtapiInstance.ACCOUNT,
            Instance.USER: UtapiInstance.USER,
            Instance.BUCKET: UtapiInstance.BUCKET,
        }

    async def get_metrics(
        self,
        instance_type: Instance,
        object_name: str,
        human: bool,
        selector: Optional[List[str]] = None,
        custom_time: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Get metrics using Utapi"""
        logger.info(f"Getting Scality metrics for {instance_type.value}: {object_name}")

        if selector is None:
            selector = []
        if custom_time is None:
            custom_time = {}

        # Convert our Instance enum to Utapi's Instance enum
        utapi_instance_type = self._instance_mapping[instance_type]

        metrics = await self.utapi.get_metrics(
            instance_type=utapi_instance_type,
            object=object_name,
            human=human,
            selector=selector,
            custom_time=custom_time,
        )

        return metrics


class ScalityConsoleProvider(BaseConsoleProvider):
    """Scality implementation of console provider using Console"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.console = Console(config["s3_endpoint_url"])
        self.console_username = config["console_username"]
        self.console_password = config["console_password"]
        self.provider = "scality"

    async def authenticate(self, console_username: str, console_password: str) -> None:
        """Authenticate with console"""
        await self.console.authenticate(console_username, console_password)

    async def create_account(
        self, account_name: str, email: str, quota: int, password: str
    ) -> AccountInstanceResponse:
        """Create account via console"""
        logger.info(f"Creating account via Scality console: {account_name}")

        await self.console.authenticate(self.console_username, self.console_password)
        response: AccountInstanceResponse = await self.console.create_account(
            account_name, email, quota, password
        )
        response.endpointUrl = self.config["s3_endpoint_url"]

        return response

    async def delete_account(self, account_name: str) -> None:
        """Delete account via console"""
        logger.info(f"Deleting account via Scality console: {account_name}")

        await self.console.authenticate(self.console_username, self.console_password)
        await self.console.delete_account(account_name)
        await self.console.delete_account_user(account_name)

    async def get_account(self, account_name: str) -> Dict[str, Any]:
        """Get account via console"""
        logger.info(f"Getting account via Scality console: {account_name}")

        await self.console.authenticate(self.console_username, self.console_password)
        return await self.console.get_account(account_name)


class ScalityS3Provider(BaseS3Provider):
    """Scality implementation of S3 provider using boto3"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.endpoint_url = config["s3_endpoint_url"]

    def get_s3_client(self, access_key: str, secret_key: str) -> Any:
        """Get S3 client for Scality"""
        return boto3.client(
            service_name="s3",
            endpoint_url=self.endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=self.region,
            verify=False,
        )

    def get_iam_client(self, access_key: str, secret_key: str) -> Any:
        """Get IAM client for Scality"""
        return boto3.client(
            service_name="iam",
            endpoint_url=self.endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=self.region,
            verify=False,
        )


class ScalityIAMProvider(BaseIAMProvider):
    """Scality implementation of IAM provider using existing Iam class"""

    def __init__(
        self, region: str, endpoint_url: str, access_key: str, secret_access_key: str
    ):
        super().__init__(region, endpoint_url, access_key, secret_access_key)
        self.iam = Iam(region, endpoint_url, access_key, secret_access_key)

    def remove_users_from_groups(self) -> None:
        """Remove users from groups"""
        self.iam.remove_users_from_groups()

    def detach_role_policies(self) -> None:
        """Detach role policies"""
        self.iam.detach_role_policies()

    def detach_group_policies(self) -> None:
        """Detach group policies"""
        self.iam.detach_group_policies()

    def detach_user_policies(self) -> None:
        """Detach user policies"""
        self.iam.detach_user_policies()

    def delete_policy_versions(self) -> None:
        """Delete policy versions"""
        self.iam.delete_policy_versions()

    def delete_policies(self) -> None:
        """Delete policies"""
        self.iam.delete_policies()

    def delete_roles(self) -> None:
        """Delete roles"""
        self.iam.delete_roles()

    def delete_groups(self) -> None:
        """Delete groups"""
        self.iam.delete_groups()

    def delete_users(self) -> None:
        """Delete users"""
        self.iam.delete_users()

    async def cleanup_account_iam(self, access_key: str, secret_key: str) -> None:
        """Clean up all IAM entities for an account"""
        # Create a new Iam instance with the account's credentials
        logger.info(f"Cleaning up Scality IAM account: {access_key}")

        iam = Iam(
            endpoint_url=self.endpoint_url,
            region=self.region,
            access_key=access_key,
            secret_access_key=secret_key,
        )

        # Clean up all IAM entities in the correct order
        iam.remove_users_from_groups()
        iam.detach_role_policies()
        iam.detach_group_policies()
        iam.detach_user_policies()
        iam.delete_policy_versions()
        iam.delete_policies()
        iam.delete_roles()
        iam.delete_groups()
        iam.delete_users()
