"""
Huawei Cloud Object Storage Service (OBS) client implementation.

This module provides a wrapper around Huawei OBS APIs for S3-compatible operations.
"""

import logging
from datetime import datetime, timedelta, timezone
from enum import Enum, auto
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import httpx
import humanfriendly
from fastapi import status
from pydantic import BaseModel

from ...exceptions import (  # noqa: F401
    AccessDenied,
    EntityAlreadyExists,
    NoSuchEntity,
)

logger = logging.getLogger(__name__)

timeout = httpx.Timeout(10.0)


class Instance(Enum):
    ACCOUNT = auto()
    USER = auto()
    BUCKET = auto()


class OBSAccountMetricsResponse(BaseModel):
    BucketCount: int
    GlobalBucketCount: int
    GlobalObjectCount: int
    GlobalSpaceSize: int
    ObjectCount: int
    Quota: int
    SpaceSize: int


class OBSClient:
    """Huawei Cloud Object Storage Service client"""

    def __init__(
        self,
        host: str,
        port: str,
        verify_https: bool,
        username: str,
        password: str,
        esn: str,
    ):
        """
        Initialize OBS client.

        Args:
            host: Huawei OBS host
            port: Huawei OBS port
            verify_https: Whether to verify HTTPS
            username: Username for authentication
            password: Password for authentication
            esn: ESN of the platform
        """
        self.host = host
        self.port = port
        self.verify_https = verify_https
        self.username = username
        self.password = password
        self.base_url = f"https://{self.host}:{self.port}/api/v2/"
        self.esn = esn
        self.headers = {}

    @staticmethod
    def check_error(r: httpx.Response) -> None:
        if r.status_code == status.HTTP_409_CONFLICT:
            raise EntityAlreadyExists("Entity already exists")

        r.raise_for_status()

        # snoop.pp(r.json())

        result = r.json()["result"]

        if isinstance(result, int):
            error_code = result
            if error_code == 0:
                return

            logger.error(
                f"Failed to execute request: {error_code} {r.json()['data']['errorMsg']}"
            )
            if error_code == 1800000409:
                raise EntityAlreadyExists(
                    f"Entity already exists or still exists: {error_code} {r.json()['data']['errorMsg']}"
                )
            if error_code == 1800000404:
                raise NoSuchEntity(
                    f"Entity does not exist: {error_code} {r.json()['data']['errorMsg']}"
                )
            raise Exception(
                f"Failed to execute request: {error_code} {r.json()['data']['errorMsg']}"
            )

        if isinstance(result, dict):
            error_code = result.get("code", None)
            if error_code is not None:
                if error_code == 0:
                    return

                logger.error(
                    f"Failed to execute request: {error_code} {r.json()['result']['description']}"
                )
                if error_code == 1800000409:
                    raise EntityAlreadyExists(
                        f"Entity already exists or still exists: {r.json()['result']['description']}"
                    )
                if error_code == 1800000404:
                    raise NoSuchEntity(
                        f"Entity does not exist: {r.json()['result']['description']}"
                    )

                raise Exception(
                    f"Failed to execute request: {error_code} {r.json()['result']['description']}"
                )

    @classmethod
    def _generate_tokens(
        cls, base_url: str, username: str, password: str, verify_https: bool
    ) -> Dict[str, str]:
        """
        Generate authentication tokens for Huawei OBS operations.

        Returns:
            Authentication token
        """
        logger.info("Generating Huawei OBS token")

        payload = {"user_name": username, "password": password}
        headers = {"Content-Type": "application/json"}

        url_session = urljoin(base_url, "aa/sessions")
        try:
            r = httpx.post(
                url_session,
                headers=headers,
                json=payload,
                verify=verify_https,
                timeout=timeout,
            )
            OBSClient.check_error(r)
            response_data = r.json()
            x_auth_token = response_data["data"]["x_auth_token"]
            x_csrf_token = response_data["data"]["x_csrf_token"]

            return {"X-Auth-Token": x_auth_token, "X-CSRF-Token": x_csrf_token}
        except Exception as e:
            logger.error(f"Failed to generate Huawei OBS token: {str(e)}")
            raise e

    async def authenticate(self) -> None:
        tokens = OBSClient._generate_tokens(
            base_url=self.base_url,
            username=self.username,
            password=self.password,
            verify_https=self.verify_https,
        )
        self.headers = {
            "X-Auth-Token": tokens["X-Auth-Token"],
            "X-CSRF-Token": tokens["X-CSRF-Token"],
            "Content-Type": "application/json",
        }

    async def create_account(self, account_name: str) -> dict[str, Any]:
        """
        Create a new account.

        Args:
            account_name: Name of the account

        Returns:
            Created account information
        """
        logger.info(f"Creating Huawei OBS account: {account_name}")

        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        try:
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.post(account_url, json=payload, headers=self.headers)
                OBSClient.check_error(r)
        except EntityAlreadyExists:
            raise EntityAlreadyExists(f"Account {account_name} already exists")
        except Exception as e:
            logger.error(f"Failed to create Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            raise Exception(f"Failed to create Huawei OBS account: {account_name}")

        return r.json()["data"]

    async def get_account(self, account_name: str) -> Optional[Dict]:
        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        try:
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.get(account_url, params=payload, headers=self.headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to get Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            return None

        return r.json()["data"]

    async def list_accounts(
        self, offset: int = 0, limit: int = 100
    ) -> List[Dict[str, Any]]:
        if offset < 0:
            raise ValueError("Offset must be non-negative")
        if limit < 1 or limit >= 1000:
            raise ValueError("Limit must be between 1 and 999")

        account_url = urljoin(self.base_url, "account/accounts")
        params = {"range": f'{{"offset":{offset},"limit":{limit}}}'}
        try:
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.get(account_url, params=params, headers=self.headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to list Huawei OBS accounts: {str(e)}")
            raise e

        return r.json()["data"]

    async def get_account_id(self, account_name: str) -> str:
        logger.info(f"Getting Huawei OBS account ID: {account_name}")

        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        try:
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.get(account_url, params=payload, headers=self.headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to get Huawei OBS account ID: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            raise NoSuchEntity(f"Account {account_name} does not exist")

        return r.json()["data"]["id"]

    async def delete_unix_users(self, account_name: str) -> None:
        logger.info(f"Deleting Unix users for account: {account_name}")

        users_url = urljoin(self.base_url, "nas_protocol/unix_user")
        params = f"account_name={account_name}" + '&range={"offset":0,"limit":100}'
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.get(users_url, params=params, headers=self.headers)
            OBSClient.check_error(r)

            for user in r.json()["data"]:
                user_name = user["name"]
                logger.debug(f"Deleting Unix user: {user_name}")
                delete_params = f"name={user_name}&account_name={account_name}"
                try:
                    r = await client.delete(
                        users_url, params=delete_params, headers=self.headers
                    )
                    OBSClient.check_error(r)
                except Exception as e:
                    logger.error(
                        f"Failed to delete user {user_name} for account {account_name}: {str(e)}"
                    )
                    raise e

    async def delete_unix_groups(self, account_name: str) -> None:
        logger.info(f"Deleting Unix groups for account: {account_name}")

        groups_url = urljoin(self.base_url, "nas_protocol/unix_group")
        params = f"account_name={account_name}" + '&range={"offset":0,"limit":100}'
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.get(groups_url, params=params, headers=self.headers)
            OBSClient.check_error(r)

            for group in r.json()["data"]:
                group_name = group["name"]
                logger.debug(f"Deleting Unix group: {group_name}")
                delete_params = f"name={group_name}&account_name={account_name}"
                try:
                    r = await client.delete(
                        groups_url, params=delete_params, headers=self.headers
                    )
                    OBSClient.check_error(r)
                except Exception as e:
                    logger.error(
                        f"Failed to delete group {group_name} for account {account_name}: {str(e)}"
                    )
                    raise e

    async def delete_account(self, account_name: str) -> None:
        logger.info(f"Deleting Huawei OBS account: {account_name}")

        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.request(
                "DELETE", account_url, headers=self.headers, json=payload
            )
            OBSClient.check_error(r)

    async def create_account_with_administrator_user(
        self, account_id: str, password: str
    ) -> None:
        base_url = f"https://{self.host}:{self.port}/"
        admin_url = urljoin(base_url, f"deviceManager/rest/{self.esn}/user")
        payload = {
            "LOGINMETHODLIST": "3,4",  # GUI, RESTFULL
            "NAME": "administrator",
            "DESCRIPTION": "Administrator",
            "PASSWORD": password,
            "ROLEID": "1024",  # Administrator
            "SCOPE": 0,
            "account_id": account_id,
        }

        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(admin_url, headers=self.headers, json=payload)
            OBSClient.check_error(r)

    async def delete_console_users_from_account(self, account_id: str) -> None:
        logger.info(f"Deleting console users from accountID: {account_id}")

        base_url = f"https://{self.host}:{self.port}/"
        admin_url = urljoin(base_url, f"deviceManager/rest/{self.esn}/user")
        payload = {"account_id": account_id}
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.get(admin_url, headers=self.headers, params=payload)
            OBSClient.check_error(r)

        for user in r.json().get("data", []):
            user_name = user["NAME"]
            logger.debug(f"Deleting console user: {user_name}")
            delete_url = urljoin(
                base_url, f"deviceManager/rest/{self.esn}/user/{user_name}"
            )
            payload = {"account_id": account_id}
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.delete(
                    delete_url, headers=self.headers, params=payload
                )
                OBSClient.check_error(r)

    async def get_metrics(
        self,
        instance_type: Instance,
        object_name: str,
        human: bool,
        selector: Optional[List[str]] = None,
        custom_time: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        logger.info(
            f"Getting Huawei OBS metrics for {instance_type.value}: {object_name}"
        )

        if selector is None or "current" not in selector:
            raise NotImplementedError(
                f"Timeframe {selector} not yet implemented for Huawei OBS"
            )

        if instance_type is Instance.ACCOUNT:
            metrics = await self.get_account_metrics(account_id=object_name)
        else:
            raise NotImplementedError(
                f"Metrics for {instance_type.value} not yet implemented"
            )

        # Huawei OBS API does not support collecting metrics from the past
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(minutes=15)
        response = {"accountId": object_name, "metrics": []}
        for sel in selector:
            logger.info(f"Processing {sel} for {object_name}")

            if sel == "current":
                response["metrics"].append(
                    {
                        "interval": sel,
                        "startDate": start_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "endDate": end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "storageUtilized": humanfriendly.format_size(
                            abs(metrics.SpaceSize), binary=True
                        )
                        if human
                        else metrics.SpaceSize,
                        "numberOfObjects": metrics.ObjectCount,
                        "incomingBytes": "",
                        "outgoingBytes": "",
                        "numberOfOperations": {},
                    }
                )
            else:
                response["metrics"].append(
                    {
                        "interval": sel,
                        "startDate": start_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "endDate": end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "storageUtilized": "",
                        "numberOfObjects": "",
                        "incomingBytes": "",
                        "outgoingBytes": "",
                        "numberOfOperations": {},
                    }
                )

        return response

    async def get_account_metrics(self, account_id: str) -> OBSAccountMetricsResponse:
        metrics_url = urljoin(
            self.base_url, f"../../dfv/service/obsPOE/accountStatistic/{account_id}"
        )
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.get(metrics_url, headers=self.headers)
            OBSClient.check_error(r)

            response = OBSAccountMetricsResponse(
                BucketCount=r.json()["data"]["BucketCount"],
                GlobalBucketCount=r.json()["data"]["GlobalBucketCount"],
                GlobalObjectCount=r.json()["data"]["GlobalObjectCount"],
                GlobalSpaceSize=r.json()["data"]["GlobalSpaceSize"],
                ObjectCount=r.json()["data"]["ObjectCount"],
                Quota=r.json()["data"]["Quota"],
                SpaceSize=r.json()["data"]["SpaceSize"],
            )
            return response

    async def create_object_user_admin(
        self, account_id: str, username: str
    ) -> Dict[str, str]:
        logger.info(f"Creating Huawei OBS object user: {username}")

        user_url = urljoin(self.base_url, "../../dfv/service/obsPOE/listUsers")
        payload = {
            "auth": {"accountId": account_id},
            "range": {"offset": 0, "limit": 100},
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(user_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

            found = any(
                user["name"] == username
                for user in r.json()["data"].get("userList", [])
            )

        if not found:
            user_url = urljoin(self.base_url, "../../dfv/service/obsPOE/user")
            payload = {
                "accountId": account_id,
                "name": username,
                "path": "/",
            }
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.post(user_url, json=payload, headers=self.headers)
                OBSClient.check_error(r)

                user_id = r.json()["data"]["id"]

            user_url = urljoin(self.base_url, "../../dfv/service/obsPOE/S3_User_Policy")
            payload = {
                "accountId": account_id,
                "name": username,
                "policyDocument": '{"Statement":[{"Sid":"readWrite","Effect":"Allow","Action":"s3:*","Resource":"*"}]}',
                "policyName": "readWrite",
            }
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.put(user_url, json=payload, headers=self.headers)
                OBSClient.check_error(r)
        else:
            user_id = next(
                user["id"]
                for user in r.json()["data"]["userList"]
                if user["name"] == username
            )

        user_url = urljoin(self.base_url, "../../dfv/service/obsPOE/S3_User_AccessKey")
        payload = {
            "accountId": account_id,
            "name": username,
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(user_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

            user_ak = r.json()["data"]["accessKeyId"]
            user_sk = r.json()["data"]["secretAccessKey"]

        return {"user_id": user_id, "access_key": user_ak, "secret_key": user_sk}

    async def delete_object_user_admin(self, account_id: str, username: str) -> None:
        logger.info(f"Deleting Huawei OBS object user: {username}")

        user_url = urljoin(
            self.base_url, "../../dfv/service/obsPOE/S3_Delete_User_Policy"
        )
        payload = {
            "accountId": account_id,
            "name": username,
            "policyName": "readWrite",
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(user_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

        user_url = urljoin(
            self.base_url, "../../dfv/service/obsPOE/S3_List_User_AccessKey"
        )
        payload = {
            "accountId": account_id,
            "name": username,
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(user_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

        for key in r.json()["data"]:
            access_key = key["accessKeyId"]
            user_url = urljoin(
                self.base_url, "../../dfv/service/obsPOE/S3_Delete_User_AccessKey"
            )
            payload = {
                "accountId": account_id,
                "name": username,
                "userAccessKeyId": access_key,
            }
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.post(user_url, json=payload, headers=self.headers)
                OBSClient.check_error(r)

        user_url = urljoin(self.base_url, "../../dfv/service/obsPOE/deleteUser")
        payload = {
            "accountId": account_id,
            "name": username,
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(user_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

    async def delete_object_user_policy(self, account_id: str, username: str) -> None:
        logger.info(f"Deleting Huawei OBS object user policy: {username}")

        url = urljoin(self.base_url, "../../dfv/service/obsPOE/S3_List_User_Policy")
        payload = {
            "auth": {"accountId": account_id},
            "name": username,
            "range": {"marker": "", "limit": 100},
        }

        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

        for policy in r.json()["data"].get("policyList", []):
            policy_name = policy
            user_url = urljoin(
                self.base_url, "../../dfv/service/obsPOE/S3_Delete_User_Policy"
            )
            payload = {
                "accountId": account_id,
                "name": username,
                "policyName": policy_name,
            }
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.post(user_url, json=payload, headers=self.headers)
                OBSClient.check_error(r)

    async def delete_object_user_access_keys(
        self, account_id: str, username: str
    ) -> None:
        logger.info(f"Deleting Huawei OBS object user access keys: {username}")

        user_url = urljoin(
            self.base_url, "../../dfv/service/obsPOE/S3_List_User_AccessKey"
        )
        payload = {
            "accountId": account_id,
            "name": username,
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(user_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

        for key in r.json()["data"]:
            access_key = key["accessKeyId"]
            user_url = urljoin(
                self.base_url,
                "../../dfv/service/obsPOE/S3_Delete_User_AccessKey",
            )
            payload = {
                "accountId": account_id,
                "name": username,
                "userAccessKeyId": access_key,
            }
            async with httpx.AsyncClient(
                verify=self.verify_https, timeout=timeout
            ) as client:
                r = await client.post(user_url, json=payload, headers=self.headers)
                OBSClient.check_error(r)

    async def delete_object_user(self, account_id: str, username: str) -> None:
        logger.info(f"Deleting Huawei OBS object user: {username}")

        user_url = urljoin(self.base_url, "../../dfv/service/obsPOE/deleteUser")
        payload = {
            "accountId": account_id,
            "name": username,
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(user_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

    async def delete_object_users(self, account_id: str) -> None:
        logger.info(f"Deleting Huawei OBS object users for account: {account_id}")

        user_url = urljoin(self.base_url, "../../dfv/service/obsPOE/listUsers")
        payload = {
            "auth": {"accountId": account_id},
            "range": {"offset": 0, "limit": 100},
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(user_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

            for user in r.json()["data"]["userList"]:
                username = user["name"]
                await self.delete_object_user_policy(account_id, username)
                await self.delete_object_user_access_keys(account_id, username)
                await self.delete_object_user(account_id, username)

    async def set_quota(self, account_id: str, quota_bytes: int) -> None:
        logger.info(f"Setting Huawei OBS quota: {account_id} - {quota_bytes}")

        quota_url = urljoin(self.base_url, "converged_service/quota")
        payload = {
            "account_id": account_id,
            "parent_type": 50,  # Hard Quota
            "space_hard_quota": str(quota_bytes * 1024),
            "space_unit_type": "1",  # KB
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(quota_url, json=payload, headers=self.headers)
            OBSClient.check_error(r)

    async def delete_quota(self, account_id: str) -> None:
        logger.info(f"Deleting Huawei OBS quota: {account_id}")

        quota_url = urljoin(self.base_url, "converged_service/quota")
        payload = {
            "account_id": account_id,
            "parent_type": 50,  # Hard Quota
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.delete(quota_url, params=payload, headers=self.headers)
            OBSClient.check_error(r)

    async def delete_account_policies(self, account_id: str) -> None:
        logger.info(f"Deleting Huawei OBS account policies: {account_id}")

        policy_url = urljoin(
            self.base_url, "../../dfv/service/obsPOE/listAccountPolicy"
        )
        payload = {
            "filter": {},
            "limit": 100,
            "offset": 0,
            "accountId": account_id,
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(
                policy_url,
                json=payload,
                headers=self.headers,
            )
            OBSClient.check_error(r)

            for policy in r.json()["data"].get("policyList", []):
                policy_url = urljoin(
                    self.base_url, "../../dfv/service/obsPOE/deleteAccountPolicy"
                )
                payload = {"accountId": account_id, "policyName": policy}

                async with httpx.AsyncClient(
                    verify=self.verify_https, timeout=timeout
                ) as client:
                    r = await client.post(
                        policy_url,
                        json=payload,
                        headers=self.headers,
                    )
                    OBSClient.check_error(r)

    async def delete_account_roles(self, account_id: str) -> None:
        logger.info(f"Deleting Huawei OBS account roles: {account_id}")

        role_url = urljoin(self.base_url, "../../dfv/service/obsPOE/listRoles")
        payload = {
            "filter": {},
            "limit": 100,
            "offset": 0,
            "accountId": account_id,
        }
        async with httpx.AsyncClient(
            verify=self.verify_https, timeout=timeout
        ) as client:
            r = await client.post(
                role_url,
                json=payload,
                headers=self.headers,
            )
            OBSClient.check_error(r)

            for role in r.json()["data"].get("roleNames", []):
                role_url = urljoin(self.base_url, "../../dfv/service/obsPOE/deleteRole")
                payload = {"accountId": account_id, "roleName": role}

                async with httpx.AsyncClient(
                    verify=self.verify_https, timeout=timeout
                ) as client:
                    r = await client.post(
                        role_url,
                        json=payload,
                        headers=self.headers,
                    )
                    OBSClient.check_error(r)
