"""
Huawei Cloud provider implementation for S3-compatible services.

This module provides Huawei Cloud Object Storage Service (OBS) integration
following the same interface as other providers.
"""

import logging
import random
from typing import Any, Dict, List, Optional

import boto3

from ..exceptions import NoSuchEntity
from ..models import AccountInstanceResponse
from .base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
    Instance,
)
from .huawei.obs_client import Instance as OBSInstance
from .huawei.obs_client import OBSClient
from .huawei.poe_client import POEClient

logger = logging.getLogger(__name__)


def generate_password():
    return "password"


class HuaweiAccountProvider(BaseAccountProvider):
    """Huawei Cloud implementation of account provider"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)

        self.obs_client = OBSClient(
            host=config["obs_host"],
            port=config["obs_port"],
            verify_https=True
            if config["obs_verify_https"] in ["True", "true"]
            else False,
            username=config["obs_username"],
            password=config["obs_password"],
            esn=config["esn"],
        )

        # Initialize Huawei OBS client
        self.poe_client = POEClient(
            endpoint=config["poe_endpoint"],
            port=config["poe_port"],
            verify_https=False,
            access_key=config["poe_access_key"],
            secret_key=config["poe_secret_key"],
        )

    async def create_account(
        self, account_name: str, account_id: str, email: str, quota: int
    ) -> AccountInstanceResponse:
        """Create account using Huawei Cloud APIs"""

        logger.info(f"Creating Huawei account: {account_name}")

        if account_id is None:
            account_id = str(
                random.randint(POEClient.MIN_ACCOUNT_ID, POEClient.MAX_ACCOUNT_ID)
            )

        r = await self.poe_client.create_account(
            account_name=account_name, account_id=account_id, email=email
        )
        await self.poe_client.set_quota_bytes(account_id=account_id, quota_bytes=quota)

        result: AccountInstanceResponse = AccountInstanceResponse(
            endpointUrl=self.config["s3_endpoint_url"],
            accountId=r["AccountId"],
            accountName=r["AccountName"],
            emailAddress=r["Email"],
            consolePassword="",
            accessKey=r["AccessKeyId"],
            secretAccessKey=r["SecretAccessKey"],
        )

        return result

    async def delete_account(self, account_name: str) -> None:
        """Delete account using Huawei Cloud APIs"""
        logger.info(f"Deleting Huawei account: {account_name}")
        await self.obs_client.authenticate()
        await self.delete_account_quota(account_name=account_name)
        await self.delete_account_access_key(account_name=account_name)
        await self.poe_client.delete_account(account_name=account_name)

    async def get_account(
        self, account_name: str, starts_with: bool = False, ends_with: bool = False
    ) -> List[Dict[str, Any]]:
        """Get account via Huawei Cloud console"""
        logger.info(f"Getting account via Huawei vault: {account_name}")
        accounts_list = []
        if not starts_with and not ends_with:
            await self.obs_client.authenticate()
            account = await self.obs_client.get_account(account_name)
            if account:
                accounts_list.append(
                    {
                        "arn": account.get("Arn", ""),  # not reported
                        "id": account.get("id", ""),
                        "name": account.get("name", ""),
                        "createDate": account.get("create_time", ""),
                        "emailAddress": account.get("Email", ""),  # not reported
                        "canonicalId": account.get("canonical_user_id", ""),
                        "quota": 0,  # Default value
                    }
                )
                return accounts_list
        else:
            limit = 100
            offset = 0
            marker = None
            while True:
                accounts = await self.poe_client.list_accounts(
                    offset=offset, limit=limit, marker=marker
                )
                for account in accounts["list"]:
                    if starts_with and account["AccountName"].startswith(account_name):
                        accounts_list.append(account)
                    elif ends_with and account["AccountName"].endswith(account_name):
                        accounts_list.append(account)
                if int(accounts["count"]) < limit:
                    break
                marker = accounts["marker"]

        if len(accounts_list) == 0:
            raise NoSuchEntity("Missing account")

        vault_accounts = [
            {
                "arn": acc.get("Arn", ""),
                "id": acc.get("AccountId", ""),
                "name": acc.get("AccountName", ""),
                "createDate": acc.get("CreateDate", ""),
                "emailAddress": acc.get("Email", ""),
                "canonicalId": acc.get("CanonicalUserId", ""),
                "quota": 0,  # Default value
            }
            for acc in accounts_list
        ]

        return vault_accounts

    async def generate_account_access_key(self, account_name: str) -> Dict[str, str]:
        """Generate access key for account using Huawei Cloud APIs"""
        logger.info(f"Generating access key for Huawei account: {account_name}")
        account_id = await self.poe_client.get_account_id(account_name)
        await self.obs_client.authenticate()
        keys = await self.obs_client.create_object_user_admin(
            account_id=account_id, username="fpadministrator"
        )
        return {"accessKey": keys["access_key"], "secretKeyValue": keys["secret_key"]}

    async def delete_account_access_key(self, account_name: str) -> None:
        """Delete access key for account using Huawei Cloud APIs"""
        logger.info(f"Deleting access key for Huawei account: {account_name}")
        account_id = await self.poe_client.get_account_id(account_name)
        await self.obs_client.authenticate()
        await self.obs_client.delete_object_user_admin(
            account_id=account_id, username="fpadministrator"
        )

    async def update_account_quota(self, account_name: str, quota_bytes: int) -> None:
        """Update account quota using Huawei Cloud APIs"""
        logger.info(
            f"Updating quota for Huawei account: {account_name} to {quota_bytes}"
        )
        account_id = await self.poe_client.get_account_id(account_name)
        await self.poe_client.set_quota_bytes(
            account_id=account_id, quota_bytes=quota_bytes
        )

    async def delete_account_quota(self, account_name: str) -> None:
        """Delete account quota using Huawei Cloud APIs"""
        logger.info(f"Deleting quota for Huawei account: {account_name}")
        await self.poe_client.empty_storage_policy(account_id=account_name)

    async def get_account_quota(self, account_name: str) -> Dict[str, Any]:
        """Get account quota using Huawei Cloud APIs"""

        logger.info(f"Getting quota for Huawei account: {account_name}")
        account_id = await self.poe_client.get_account_id(account_name)
        quota_bytes = await self.poe_client.get_quota_bytes(account_id=account_id)
        return {"quota": quota_bytes}


class HuaweiMetricsProvider(BaseMetricsProvider):
    """Huawei Cloud implementation of metrics provider"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)

        self.obs_client = OBSClient(
            host=self.config["obs_host"],
            port=self.config["obs_port"],
            verify_https=True
            if config["obs_verify_https"] in ["True", "true"]
            else False,
            username=self.config["obs_username"],
            password=self.config["obs_password"],
            esn=self.config["esn"],
        )

        # Mapping from base Instance enum to OBS Instance enum
        self._instance_mapping = {
            Instance.ACCOUNT: OBSInstance.ACCOUNT,
            Instance.USER: OBSInstance.USER,
            Instance.BUCKET: OBSInstance.BUCKET,
        }

    async def get_metrics(
        self,
        instance_type: Instance,
        object_name: str,
        human: bool,
        selector: Optional[List[str]] = None,
        custom_time: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Get metrics"""

        logger.info(f"Getting Huawei metrics for {instance_type.value}: {object_name}")

        # Convert base Instance enum to OBS Instance enum
        obs_instance_type = self._instance_mapping[instance_type]

        await self.obs_client.authenticate()
        metrics = await self.obs_client.get_metrics(
            instance_type=obs_instance_type,
            object_name=object_name,
            human=human,
            selector=selector,
            custom_time=custom_time,
        )

        return metrics


class HuaweiConsoleProvider(BaseConsoleProvider):
    """Huawei Cloud implementation of console provider"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.obs_client = OBSClient(
            host=config["obs_host"],
            port=config["obs_port"],
            verify_https=True
            if config["obs_verify_https"] in ["True", "true"]
            else False,
            username=config["obs_username"],
            password=config["obs_password"],
            esn=config["esn"],
        )

        self.provider = "huawei"

    async def authenticate(self) -> None:
        """Authenticate with Huawei OBS console"""
        await self.obs_client.authenticate()

    async def create_account(
        self, account_name: str, email: str, quota: int, password: str
    ) -> AccountInstanceResponse:
        """Create account via Huawei Cloud console"""
        logger.info(f"Creating account via Huawei console: {account_name}")

        await self.authenticate()

        response = await self.obs_client.create_account(account_name=account_name)
        await self.obs_client.create_account_with_administrator_user(
            account_id=response["id"], password=password
        )

        await self.obs_client.set_quota(account_id=response["id"], quota_bytes=quota)

        return AccountInstanceResponse(
            endpointUrl=self.config["s3_endpoint_url"],
            accountId=response["id"],
            accountName=response["name"],
            emailAddress=email,
            consolePassword=password,
            accessKey=response["access_key"],
            secretAccessKey=response["security_key"],
        )

    async def delete_account(self, account_name: str) -> None:
        """Delete account via Huawei Cloud console"""
        logger.info(f"Deleting account via Huawei console: {account_name}")

        await self.authenticate()

        account_id = await self.obs_client.get_account_id(account_name)
        await self.obs_client.delete_unix_users(account_name)
        await self.obs_client.delete_unix_groups(account_name)
        await self.obs_client.delete_quota(account_id=account_id)
        await self.obs_client.delete_account_policies(account_id=account_id)
        await self.obs_client.delete_account_roles(account_id=account_id)
        await self.obs_client.delete_object_users(account_id=account_id)
        await self.obs_client.delete_console_users_from_account(account_id)
        await self.obs_client.delete_account(account_name)

    async def get_account(
        self, account_name: str, starts_with: bool = False, ends_with: bool = False
    ) -> List[Dict[str, Any]]:
        logger.info(f"Getting account via Huawei console: {account_name}")

        await self.authenticate()

        accounts_list = []
        if not starts_with and not ends_with:
            account = await self.obs_client.get_account(account_name)
            if account:
                accounts_list.append(account)
        else:
            limit = 100
            offset = 0
            while True:
                accounts = await self.obs_client.list_accounts(
                    offset=offset, limit=limit
                )
                for account in accounts:
                    if starts_with and account["name"].startswith(account_name):
                        accounts_list.append(account)
                    elif ends_with and account["name"].endswith(account_name):
                        accounts_list.append(account)
                if len(accounts) < limit:
                    break
                offset += limit

        if len(accounts_list) == 0:
            raise NoSuchEntity("Missing account")

        return accounts_list


class HuaweiS3Provider(BaseS3Provider):
    """Huawei Cloud implementation of S3 provider using Boto3"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.endpoint_url = config["s3_endpoint_url"]

    def get_s3_client(self, access_key: str, secret_key: str) -> Any:
        """Get S3 client for Scality"""
        return boto3.client(
            service_name="s3",
            endpoint_url=self.endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=self.region,
            verify=False,
        )

    def get_iam_client(self, access_key: str, secret_key: str) -> Any:
        """Get IAM client for Scality"""
        return boto3.client(
            service_name="iam",
            endpoint_url=self.endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=self.region,
            verify=False,
        )


class HuaweiIAMProvider(BaseIAMProvider):
    """Huawei Cloud implementation of IAM provider"""

    def __init__(
        self, region: str, endpoint_url: str, access_key: str, secret_access_key: str
    ):
        super().__init__(region, endpoint_url, access_key, secret_access_key)
        # TODO: Initialize Huawei IAM client
        logger.info("Initializing Huawei IAM provider")

    async def remove_users_from_groups(self) -> None:
        return super().remove_users_from_groups()

    async def detach_role_policies(self) -> None:
        return super().detach_role_policies()

    async def detach_group_policies(self) -> None:
        return super().detach_group_policies()

    async def detach_user_policies(self) -> None:
        return super().detach_user_policies()

    async def delete_policy_versions(self) -> None:
        return super().delete_policy_versions()

    async def delete_policies(self) -> None:
        return super().delete_policies()

    async def delete_roles(self) -> None:
        return super().delete_roles()

    async def delete_groups(self) -> None:
        return super().delete_groups()

    async def delete_users(self) -> None:
        return super().delete_users()

    async def create_user(self, username: str, password: str) -> Dict[str, Any]:
        """Create IAM user using Huawei Cloud APIs"""
        # TODO: Implement Huawei IAM user creation
        logger.info(f"Creating Huawei IAM user: {username}")
        raise NotImplementedError("Huawei IAM user creation not yet implemented")

    async def delete_user(self, username: str) -> None:
        """Delete IAM user using Huawei Cloud APIs"""
        # TODO: Implement Huawei IAM user deletion
        logger.info(f"Deleting Huawei IAM user: {username}")
        raise NotImplementedError("Huawei IAM user deletion not yet implemented")

    async def create_access_key(self, username: str) -> Dict[str, str]:
        """Create access key for IAM user using Huawei Cloud APIs"""
        # TODO: Implement Huawei IAM access key creation
        logger.info(f"Creating access key for Huawei IAM user: {username}")
        raise NotImplementedError("Huawei IAM access key creation not yet implemented")

    async def delete_access_key(self, username: str, access_key_id: str) -> None:
        """Delete access key for IAM user using Huawei Cloud APIs"""
        # TODO: Implement Huawei IAM access key deletion
        logger.info(f"Deleting access key for Huawei IAM user: {username}")
        raise NotImplementedError("Huawei IAM access key deletion not yet implemented")

    async def cleanup_account_iam(self, access_key: str, secret_key: str) -> None:
        """Clean up all IAM entities for an account"""
        logger.info(f"Cleaning up Huawei IAM account: {access_key}")
        pass
