import functools
import inspect
import logging
import time

import httpx
from httpx import ConnectTimeout
from tenacity import (  # type: ignore
    retry,
    retry_if_exception_type,  # type: ignore
    stop_after_attempt,
)

from ...exceptions import AccessDenied, EntityAlreadyExists, NoSuchEntity
from ...models import AccountInstanceResponse

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def log_decorator(func):
    if inspect.iscoroutinefunction(func):

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger.debug(
                f"Calling {func.__name__} (async) with args: {args}, kwargs: {kwargs}"
            )
            try:
                result = await func(*args, **kwargs)
                logger.debug(f"{func.__name__} returned: {result}")
                return result
            except Exception as e:
                logger.debug(f"Exception in {func.__name__}: {e}")
                raise

        return async_wrapper
    else:

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger.debug(f"Calling {func.__name__} with args: {args}, kwargs: {kwargs}")
            try:
                result = func(*args, **kwargs)
                logger.debug(f"{func.__name__} returned: {result}")
                return result
            except Exception as e:
                logger.debug(f"Exception in {func.__name__}: {e}")
                raise

        return sync_wrapper


class Console:
    def __init__(self, url: str):
        self.url: str = url
        self.token: str = ""

    async def authenticate(self, console_username: str, console_password: str):
        endpoint = "{}/_/console/authenticate".format(self.url)
        headers = {"accept": "application/json", "Content-Type": "application/json"}
        request_parameters = {
            "username": console_username,
            "password": console_password,
        }
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                r = await client.post(
                    endpoint,
                    json=request_parameters,
                    headers=headers,
                )
            r.raise_for_status()

        except Exception as e:
            raise e

        if r.json()["success"] is False:
            raise AccessDenied(r.json()["message"])

        self.token = r.json()["token"]

    @retry(retry=retry_if_exception_type(ConnectTimeout), stop=stop_after_attempt(3))
    @log_decorator
    async def create_account(
        self,
        account_name: str,
        email: str,
        quota: int,
        password: str,
    ) -> AccountInstanceResponse:
        endpoint = "{}/_/console/vault/accounts".format(self.url)
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
            "x-access-token": self.token,
        }
        request_parameters = {
            "accountName": account_name,
            "email": email,
            "quota": quota,
            "password": password,
        }
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                r = await client.post(
                    endpoint,
                    json=request_parameters,
                    headers=headers,
                )

            if r.status_code == 403:
                raise AccessDenied("Access Denied")
            if r.status_code == 409:
                raise EntityAlreadyExists(
                    f"Account or email address for {account_name} already exists"
                )
            r.raise_for_status()
        except Exception as e:
            logging.error(str(e))
            raise e

        response: AccountInstanceResponse = AccountInstanceResponse(
            endpointUrl="",
            accountId=r.json()["account"]["id"],
            accountName=r.json()["account"]["name"],
            emailAddress=r.json()["account"]["emailAddress"],
            consolePassword=password,
            accessKey=r.json()["credentials"]["id"],
            secretAccessKey=r.json()["credentials"]["value"],
        )

        return response

    @retry(retry=retry_if_exception_type(ConnectTimeout), stop=stop_after_attempt(3))
    @log_decorator
    async def delete_account(
        self,
        account_name: str,
    ):
        endpoint = "{}/_/console/vault/accounts/{}".format(self.url, account_name)
        headers = {"accept": "*/*", "x-access-token": self.token}
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                r = await client.delete(
                    endpoint,
                    headers=headers,
                )

            if r.status_code == 404:
                raise NoSuchEntity(f"Entity {account_name} does not exist")

            r.raise_for_status()
        except Exception as e:
            logger.error(str(e))
            raise e

    @retry(retry=retry_if_exception_type(ConnectTimeout), stop=stop_after_attempt(3))
    @log_decorator
    async def delete_account_user(
        self,
        account_name: str,
    ):
        endpoint = "{}/_/console/vault/accounts/{}/user".format(self.url, account_name)
        headers = {"accept": "*/*", "x-access-token": self.token}
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                r = await client.delete(
                    endpoint,
                    headers=headers,
                )

            if r.status_code == 404:
                raise NoSuchEntity(f"Entity {account_name} does not exist")

            r.raise_for_status()
        except Exception as e:
            logger.error(str(e))
            raise e

    @retry(retry=retry_if_exception_type(ConnectTimeout), stop=stop_after_attempt(3))
    @log_decorator
    async def list_accounts(self, marker: str):
        endpoint = "{}/_/console/vault/accounts/100/{}".format(self.url, marker)
        headers = {"accept": "application/json", "x-access-token": self.token}
        try:
            async with httpx.AsyncClient(timeout=120.0) as client:
                r = await client.get(
                    endpoint,
                    headers=headers,
                )
        except Exception as e:
            logger.error(str(e))
            raise e

        return r.json()

    @log_decorator
    async def get_account(self, account_name: str):
        account = None
        count = 0
        start_time = time.time()
        try:
            marker = "************"
            while True:
                # Get 100 accounts
                resp = await self.list_accounts(marker=marker)

                accounts = resp["accounts"]
                account = next(
                    (
                        account
                        for account in accounts
                        if account["name"] == account_name
                    ),
                    None,  # type: ignore
                )
                if account is not None:
                    break
                else:
                    if resp["marker"] == "":  # we traversed all accounts
                        raise NoSuchEntity("Missing account")
                    else:
                        marker = resp["marker"]  # paginates
                        count += 100
                        current_time = time.time()
                        logger.debug(
                            f"Processed {count} accounts in {str(current_time - start_time)}secs"
                        )

        except Exception as e:
            logger.exception(str(e))
            raise e

        return account
