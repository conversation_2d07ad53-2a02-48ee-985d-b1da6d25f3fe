[fr-lab]
provider = scality
console_username = XXXXXXXXXXXXXXXXXXXX
console_password = XXXXXXXXXXXXXXXXXXXX
utapi_host = s3.fr-lab.jaguar-network.com
utapi_port = 8100
utapi_access_key = XXXXXXXXXXXXXXXXXXXX
utapi_secret_key = XXXXXXXXXXXXXXXXXXXX
vault_host = s3.fr-lab.jaguar-network.com
vault_port = 8600
vault_access_key = XXXXXXXXXXXXXXXXXXXX
vault_secret_key = XXXXXXXXXXXXXXXXXXXX
iam_host = s3.fr-lab.jaguar-network.com
iam_port = 9443
s3_endpoint_url = https://s3.fr-lab.jaguar-network.com

[huawei-region]
provider = huawei
obs_verify_https = false
obs_host = ************
obs_port = 8088
obs_username = XXXXXXXXXXXXXXXXXXXX
obs_password = XXXXXXXXXXXXXXXXXXXX
poe_access_key = XXXXXXXXXXXXXXXXXXXX
poe_secret_key = XXXXXXXXXXXXXXXXXXXX
poe_endpoint = ************
poe_port = 9443
esn = b04f4bc020120854
iam_host = ************
iam_port = 9443
s3_endpoint_url = https://s3.jnlab.net

[vault]
url = https://vault.jnlab.net
mount_point = sys-kv-1
path_prefix = infra/storage/object
approle_id = XXXXXXXXXXXXXXXXXXXX
approle_secret_id = XXXXXXXXXXXXXXXXXXXX
verify = true
si_username = service-account-m2m-jso-si